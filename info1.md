# 🛍️ دليل شامل لـ Products API - مثال عملي للتعديل البرمجي

## 📋 **نظرة عامة على API**

سنأخذ **Products API** كمثال عملي من مشروعك لتوضيح كيفية التعديل البرمجي على APIs موجودة.

### **🔗 المسار الحالي:**
```
GET /api/products
```

### **📁 الملفات المرتبطة:**
- **Route**: `routes/api.php` (السطر 272)
- **Controller**: `app/Http/Controllers/Api/HomeApiController.php`
- **Method**: `getProducts()`
- **Resource**: `app/Http/Resources/ProductHomeResource.php`
- **Model**: `app/Models/Product.php`

---

## 🔍 **تحليل الكود الحالي**

### **1️⃣ تعريف Route:**
```php
// في routes/api.php
Route::get('/products', [\App\Http\Controllers\Api\HomeApiController::class, 'getProducts']);
```

### **2️⃣ Controller Method الحالي:**
```php
public function getProducts(Request $request)
{
    // التحقق من product_id محدد
    if ($request->product_id) {
        $productId = $request->product_id;
        $cacheKey = 'main_page_products_' . $productId;
        
        $data = Cache::remember($cacheKey, 300, function () use ($productId) {
            $product = Product::isAllActive()->findOrFail($productId);
            return ['product' => new ProductResource($product)];
        });
        return $this->sendResponse($data, '');
    }

    // معاملات البحث والفلترة
    $categoryId = $request->category_id ?? '';
    $storeId = $request->store_id ?? '';
    $searchName = $request->search_name ?? '';
    $type = $request->type ?? '';
    $brandId = $request->brand_id ?? '';
    $orderBy = $request->order_by ?? '';
    $page = $request->page ?? '';
    
    // Cache key للفلاتر
    $cacheKey = "main_page_products_filter_category:$categoryId:storeId:$storeId:searchName:$searchName:type:$type:brandId:$brandId:orderBy:$orderBy:page:$page";
    
    $result = Cache::remember($cacheKey, 600, function () use ($request) {
        $products = Product::where('status', 1)->inRandomOrder();
        
        // تطبيق الفلاتر
        if ($request->category_id) {
            $products->getByCategory($request->category_id);
        }
        
        if ($request->store_id) {
            $products->where('store_id', $request->store_id);
        }
        
        if ($request->search_name) {
            $products->onProductTitle($request->search_name);
        }
        
        // أنواع المنتجات
        if ($request->type == 'new') {
            $products->new();
        } elseif ($request->type == 'offer') {
            $products->offer();
        } elseif ($request->type == 'discount') {
            $products->discount();
        }
        
        if ($request->brand_id) {
            $products->where('brand_id', $request->brand_id);
        }
        
        // ترتيب حسب الفئة
        if ($request->has('order_by') && $request->order_by > 0 && $request->order_by != 'new') {
            $stores = Store::where('category_id', $request->order_by)->get();
            $storeIds = $stores->pluck('id');
            $products->whereIn('store_id', $storeIds)->inRandomOrder();
        }
        
        // Pagination
        $products = $products->paginate(12);
        
        return [
            'product' => ProductHomeResource::collection($products),
            "pagination" => [
                "i_total_objects" => $products->count(),
                "i_items_on_page" => $products->count(),
                "i_per_pages" => $products->perPage(),
                "i_current_page" => $products->currentPage(),
                "i_total_pages" => $products->total()
            ]
        ];
    });
    
    return $this->sendResponse($result, '');
}
```

### **3️⃣ ProductHomeResource:**
```php
public function toArray($request)
{
    $product_reviews_user_count = $this->reviews()->count('customer_id') ?? 0;
    $product_reviews = $this->reviews()->avg('rating') ?? 0;
    
    $is_favorite = false;
    if (auth('customers')->check()) {
        $favorite = Favorite::where('product_id', $this->id)
            ->where('customer_id', auth('customers')->id())
            ->first();
        $is_favorite = $favorite ? true : false;
    }
    
    return [
        'id' => $this->id,
        'title' => $this->title,
        'price' => getRound($this->price),
        'is_available' => $this->stock_quantity > 0 ? 1 : 0,
        'description' => $this->description,
        'is_new' => $this->is_new,
        'is_offer' => $this->offer_id && $this->new_price,
        'new_price' => getRound($this->new_price),
        'currency_id' => $this->currency_id,
        'image_url' => $this->generateImageUrl(),
        'category_id' => $this->category_id,
        'is_favorite' => $is_favorite,
        'offer_discount_rate' => $this->offer_discount_rate,
    ];
}
```

---

## 🛠️ **سيناريوهات التعديل البرمجي**

### **🎯 السيناريو الأول: إضافة فلتر جديد (Price Range)**

#### **1️⃣ تعديل Controller:**
```php
// إضافة فلتر السعر في getProducts()
if ($request->min_price || $request->max_price) {
    $minPrice = $request->min_price ?? 0;
    $maxPrice = $request->max_price ?? 999999;
    
    $products->where(function($query) use ($minPrice, $maxPrice) {
        $query->whereBetween('price', [$minPrice, $maxPrice])
              ->orWhereBetween('new_price', [$minPrice, $maxPrice]);
    });
}
```

#### **2️⃣ تحديث Cache Key:**
```php
$minPrice = $request->min_price ?? '';
$maxPrice = $request->max_price ?? '';
$cacheKey = "main_page_products_filter_category:$categoryId:storeId:$storeId:searchName:$searchName:type:$type:brandId:$brandId:orderBy:$orderBy:page:$page:minPrice:$minPrice:maxPrice:$maxPrice";
```

#### **3️⃣ إضافة Validation:**
```php
$request->validate([
    'min_price' => 'nullable|numeric|min:0',
    'max_price' => 'nullable|numeric|min:0|gte:min_price',
    'category_id' => 'nullable|exists:categories,id',
    'store_id' => 'nullable|exists:stores,id',
    'brand_id' => 'nullable|exists:brands,id',
]);
```

### **🎯 السيناريو الثاني: إضافة معلومات جديدة للـ Response**

#### **1️⃣ تعديل ProductHomeResource:**
```php
public function toArray($request)
{
    // الكود الموجود...
    
    // إضافة معلومات جديدة
    $store_info = $this->store ? [
        'id' => $this->store->id,
        'name' => $this->store->name,
        'logo' => $this->store->logo_url,
        'rating' => $this->store->average_rating,
    ] : null;
    
    $reviews_summary = [
        'average_rating' => round($this->reviews()->avg('rating'), 1),
        'total_reviews' => $this->reviews()->count(),
        'five_star' => $this->reviews()->where('rating', 5)->count(),
        'four_star' => $this->reviews()->where('rating', 4)->count(),
    ];
    
    return [
        // البيانات الموجودة...
        'store_info' => $store_info,
        'reviews_summary' => $reviews_summary,
        'tags' => $this->tags ? explode(',', $this->tags) : [],
        'created_at' => $this->created_at->format('Y-m-d'),
        'updated_at' => $this->updated_at->format('Y-m-d'),
    ];
}
```

### **🎯 السيناريو الثالث: إضافة Sorting متقدم**

#### **1️⃣ تحسين منطق الترتيب:**
```php
// في getProducts() method
$allowedSortFields = ['price', 'created_at', 'title', 'rating', 'popularity'];
$sortField = $request->sort_by ?? 'created_at';
$sortDirection = $request->sort_direction ?? 'desc';

if (in_array($sortField, $allowedSortFields)) {
    switch ($sortField) {
        case 'price':
            $products->orderByRaw('CASE WHEN new_price > 0 THEN new_price ELSE price END ' . $sortDirection);
            break;
        case 'rating':
            $products->leftJoin('reviews', 'products.id', '=', 'reviews.product_id')
                    ->selectRaw('products.*, AVG(reviews.rating) as avg_rating')
                    ->groupBy('products.id')
                    ->orderBy('avg_rating', $sortDirection);
            break;
        case 'popularity':
            $products->orderBy('sell_number', $sortDirection);
            break;
        default:
            $products->orderBy($sortField, $sortDirection);
    }
} else {
    $products->inRandomOrder();
}
```

### **🎯 السيناريو الرابع: إضافة Endpoint جديد للمنتجات المميزة**

#### **1️⃣ إضافة Route جديد:**
```php
// في routes/api.php
Route::get('/products/featured', [\App\Http\Controllers\Api\HomeApiController::class, 'getFeaturedProducts']);
```

#### **2️⃣ إضافة Method جديد:**
```php
public function getFeaturedProducts(Request $request)
{
    $cacheKey = 'featured_products_' . ($request->page ?? 1);
    
    $result = Cache::remember($cacheKey, 1800, function () use ($request) {
        $products = Product::isAllActive()
            ->where('is_featured', true)
            ->with(['store', 'reviews', 'category'])
            ->orderBy('featured_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->paginate(8);
        
        return [
            'products' => ProductHomeResource::collection($products),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'total_pages' => $products->lastPage(),
                'total_items' => $products->total(),
                'per_page' => $products->perPage(),
            ]
        ];
    });
    
    return $this->sendResponse($result, 'تم جلب المنتجات المميزة بنجاح');
}
```

### **🎯 السيناريو الخامس: إضافة Rate Limiting**

#### **1️⃣ إنشاء Middleware جديد:**
```php
// app/Http/Middleware/ProductApiRateLimit.php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class ProductApiRateLimit
{
    public function handle(Request $request, Closure $next)
    {
        $key = 'products_api_' . $request->ip();
        
        if (RateLimiter::tooManyAttempts($key, 100)) {
            return response()->json([
                'success' => false,
                'message' => 'تم تجاوز الحد المسموح من الطلبات. حاول مرة أخرى بعد دقيقة.',
                'retry_after' => RateLimiter::availableIn($key)
            ], 429);
        }
        
        RateLimiter::hit($key, 60); // 100 requests per minute
        
        return $next($request);
    }
}
```

#### **2️⃣ تطبيق Middleware على Routes:**
```php
// في routes/api.php
Route::middleware(['api.language', 'product.rate.limit'])->group(function() {
    Route::get('/products', [\App\Http\Controllers\Api\HomeApiController::class, 'getProducts']);
    Route::get('/products/featured', [\App\Http\Controllers\Api\HomeApiController::class, 'getFeaturedProducts']);
});
```

---

## 🧪 **اختبار التعديلات**

### **1️⃣ اختبار الفلاتر الجديدة:**
```bash
# اختبار فلتر السعر
curl -X GET "http://localhost:8000/api/products?min_price=100&max_price=500"

# اختبار الترتيب الجديد
curl -X GET "http://localhost:8000/api/products?sort_by=price&sort_direction=asc"

# اختبار المنتجات المميزة
curl -X GET "http://localhost:8000/api/products/featured"
```

### **2️⃣ اختبار Rate Limiting:**
```bash
# إرسال طلبات متعددة لاختبار الحد
for i in {1..105}; do
    curl -X GET "http://localhost:8000/api/products"
done
```

---

## 📊 **مراقبة الأداء**

### **1️⃣ إضافة Logging للأداء:**
```php
public function getProducts(Request $request)
{
    $startTime = microtime(true);
    
    // منطق الـ API...
    
    $executionTime = microtime(true) - $startTime;
    \Log::info('Products API Performance', [
        'execution_time' => $executionTime,
        'memory_usage' => memory_get_peak_usage(true),
        'filters' => $request->only(['category_id', 'store_id', 'search_name']),
        'cache_hit' => Cache::has($cacheKey)
    ]);
    
    return $this->sendResponse($result, '');
}
```

### **2️⃣ إضافة Database Query Monitoring:**
```php
// في AppServiceProvider
public function boot()
{
    if (app()->environment('local')) {
        DB::listen(function ($query) {
            if ($query->time > 1000) { // أكثر من ثانية
                \Log::warning('Slow Query Detected', [
                    'sql' => $query->sql,
                    'bindings' => $query->bindings,
                    'time' => $query->time
                ]);
            }
        });
    }
}
```

---

## 🔧 **أدوات التطوير المساعدة**

### **1️⃣ إنشاء Artisan Command للاختبار:**
```php
// app/Console/Commands/TestProductsApi.php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestProductsApi extends Command
{
    protected $signature = 'test:products-api';
    protected $description = 'Test Products API endpoints';

    public function handle()
    {
        $baseUrl = config('app.url') . '/api';
        
        $this->info('Testing Products API...');
        
        // اختبار الـ endpoint الأساسي
        $response = Http::get($baseUrl . '/products');
        $this->line('Basic endpoint: ' . ($response->successful() ? '✅ Success' : '❌ Failed'));
        
        // اختبار الفلاتر
        $response = Http::get($baseUrl . '/products?category_id=1');
        $this->line('Category filter: ' . ($response->successful() ? '✅ Success' : '❌ Failed'));
        
        // اختبار البحث
        $response = Http::get($baseUrl . '/products?search_name=test');
        $this->line('Search filter: ' . ($response->successful() ? '✅ Success' : '❌ Failed'));
        
        $this->info('Testing completed!');
    }
}
```

### **2️⃣ استخدام الأمر:**
```bash
php artisan test:products-api
```

---

## 📝 **أفضل الممارسات للتعديل**

### **✅ Do's (افعل):**
1. **استخدم Cache** للاستعلامات المتكررة
2. **اكتب Validation** لجميع المدخلات
3. **استخدم Resources** لتنسيق البيانات
4. **اكتب Tests** للتعديلات الجديدة
5. **راقب الأداء** باستمرار
6. **استخدم Rate Limiting** للحماية

### **❌ Don'ts (لا تفعل):**
1. **لا تعدل** على الـ Database مباشرة في Controller
2. **لا تنس** تحديث Cache Keys عند إضافة فلاتر
3. **لا تتجاهل** Error Handling
4. **لا تضع** منطق معقد في Resources
5. **لا تنس** تحديث Documentation

---

## 🚀 **خطوات التطبيق العملي**

### **1️⃣ التحضير:**
```bash
# إنشاء backup للملفات
cp app/Http/Controllers/Api/HomeApiController.php app/Http/Controllers/Api/HomeApiController.php.backup
cp app/Http/Resources/ProductHomeResource.php app/Http/Resources/ProductHomeResource.php.backup
```

### **2️⃣ التطبيق:**
```bash
# تطبيق التعديلات تدريجياً
# 1. إضافة الفلاتر الجديدة
# 2. تحديث Resources
# 3. إضافة Middleware
# 4. اختبار التعديلات
```

### **3️⃣ الاختبار:**
```bash
# تشغيل الاختبارات
php artisan test
php artisan test:products-api

# مراجعة الـ logs
tail -f storage/logs/laravel.log
```

---

## 🔄 **مثال كامل: إضافة ميزة Wishlist للمنتجات**

### **المتطلب:** إضافة إمكانية حفظ المنتجات في قائمة الأمنيات

#### **1️⃣ إنشاء Migration جديد:**
```bash
php artisan make:migration create_wishlists_table
```

```php
// database/migrations/xxxx_create_wishlists_table.php
public function up()
{
    Schema::create('wishlists', function (Blueprint $table) {
        $table->id();
        $table->foreignId('customer_id')->constrained()->onDelete('cascade');
        $table->foreignId('product_id')->constrained()->onDelete('cascade');
        $table->timestamp('created_at')->useCurrent();
        $table->unique(['customer_id', 'product_id']);
    });
}
```

#### **2️⃣ إنشاء Model:**
```php
// app/Models/Wishlist.php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Wishlist extends Model
{
    protected $fillable = ['customer_id', 'product_id'];
    public $timestamps = false;

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
```

#### **3️⃣ إضافة Routes جديدة:**
```php
// في routes/api.php
Route::group(['middleware' => ['auth:customers'], 'prefix' => 'auth'], function () {
    // Wishlist routes
    Route::get('/wishlist', [\App\Http\Controllers\Api\WishlistController::class, 'index']);
    Route::post('/wishlist/toggle', [\App\Http\Controllers\Api\WishlistController::class, 'toggle']);
    Route::delete('/wishlist/{product_id}', [\App\Http\Controllers\Api\WishlistController::class, 'remove']);
});
```

#### **4️⃣ إنشاء WishlistController:**
```php
// app/Http/Controllers/Api/WishlistController.php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseControllerApi;
use App\Models\Wishlist;
use App\Models\Product;
use App\Http\Resources\ProductHomeResource;
use Illuminate\Http\Request;

class WishlistController extends BaseControllerApi
{
    public function index(Request $request)
    {
        $wishlistItems = Wishlist::where('customer_id', auth('customers')->id())
            ->with('product')
            ->paginate(15);

        $products = $wishlistItems->map(function($item) {
            return $item->product;
        });

        $result = [
            'products' => ProductHomeResource::collection($products),
            'pagination' => [
                'current_page' => $wishlistItems->currentPage(),
                'total_pages' => $wishlistItems->lastPage(),
                'total_items' => $wishlistItems->total(),
            ]
        ];

        return $this->sendResponse($result, 'تم جلب قائمة الأمنيات بنجاح');
    }

    public function toggle(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $customerId = auth('customers')->id();
        $productId = $request->product_id;

        $wishlistItem = Wishlist::where('customer_id', $customerId)
            ->where('product_id', $productId)
            ->first();

        if ($wishlistItem) {
            $wishlistItem->delete();
            $message = 'تم إزالة المنتج من قائمة الأمنيات';
            $inWishlist = false;
        } else {
            Wishlist::create([
                'customer_id' => $customerId,
                'product_id' => $productId
            ]);
            $message = 'تم إضافة المنتج إلى قائمة الأمنيات';
            $inWishlist = true;
        }

        return $this->sendResponse([
            'in_wishlist' => $inWishlist,
            'product_id' => $productId
        ], $message);
    }
}
```

#### **5️⃣ تحديث ProductHomeResource:**
```php
// إضافة في toArray method
public function toArray($request)
{
    // الكود الموجود...

    $in_wishlist = false;
    if (auth('customers')->check()) {
        $in_wishlist = Wishlist::where('customer_id', auth('customers')->id())
            ->where('product_id', $this->id)
            ->exists();
    }

    return [
        // البيانات الموجودة...
        'in_wishlist' => $in_wishlist,
    ];
}
```

---

## 🧪 **اختبارات شاملة للـ API**

### **1️⃣ إنشاء Feature Tests:**
```php
// tests/Feature/ProductsApiTest.php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Product;
use App\Models\Customer;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductsApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_get_products_list()
    {
        Product::factory()->count(5)->create();

        $response = $this->getJson('/api/products');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'product' => [
                            '*' => [
                                'id',
                                'title',
                                'price',
                                'image_url',
                                'is_favorite'
                            ]
                        ],
                        'pagination'
                    ]
                ]);
    }

    public function test_can_filter_products_by_category()
    {
        $product = Product::factory()->create(['category_id' => 1]);

        $response = $this->getJson('/api/products?category_id=1');

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data.product'));
    }

    public function test_can_search_products()
    {
        Product::factory()->create(['title' => 'iPhone 15']);
        Product::factory()->create(['title' => 'Samsung Galaxy']);

        $response = $this->getJson('/api/products?search_name=iPhone');

        $response->assertStatus(200);
        $this->assertEquals('iPhone 15', $response->json('data.product.0.title'));
    }

    public function test_authenticated_user_can_add_to_wishlist()
    {
        $customer = Customer::factory()->create();
        $product = Product::factory()->create();

        $response = $this->actingAs($customer, 'customers')
            ->postJson('/api/auth/wishlist/toggle', [
                'product_id' => $product->id
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => ['in_wishlist' => true]
                ]);
    }
}
```

### **2️⃣ تشغيل الاختبارات:**
```bash
# تشغيل جميع اختبارات الـ API
php artisan test --filter=ProductsApiTest

# تشغيل اختبار محدد
php artisan test --filter=test_can_get_products_list

# تشغيل مع تقرير التغطية
php artisan test --coverage
```

---

## 📈 **مراقبة ومتابعة الأداء**

### **1️⃣ إضافة Metrics Collection:**
```php
// app/Http/Middleware/ApiMetrics.php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ApiMetrics
{
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);

        $response = $next($request);

        $executionTime = microtime(true) - $startTime;

        // حفظ metrics في Cache
        $endpoint = $request->path();
        $date = now()->format('Y-m-d');
        $hour = now()->format('H');

        $metricsKey = "api_metrics:{$endpoint}:{$date}:{$hour}";

        $metrics = Cache::get($metricsKey, [
            'total_requests' => 0,
            'total_time' => 0,
            'avg_time' => 0,
            'max_time' => 0,
            'min_time' => PHP_FLOAT_MAX
        ]);

        $metrics['total_requests']++;
        $metrics['total_time'] += $executionTime;
        $metrics['avg_time'] = $metrics['total_time'] / $metrics['total_requests'];
        $metrics['max_time'] = max($metrics['max_time'], $executionTime);
        $metrics['min_time'] = min($metrics['min_time'], $executionTime);

        Cache::put($metricsKey, $metrics, 86400); // 24 hours

        return $response;
    }
}
```

### **2️⃣ إنشاء Dashboard للمراقبة:**
```php
// app/Http/Controllers/Api/MetricsController.php
public function getApiMetrics(Request $request)
{
    $date = $request->date ?? now()->format('Y-m-d');
    $endpoint = $request->endpoint ?? 'products';

    $metrics = [];
    for ($hour = 0; $hour < 24; $hour++) {
        $key = "api_metrics:{$endpoint}:{$date}:" . str_pad($hour, 2, '0', STR_PAD_LEFT);
        $hourlyMetrics = Cache::get($key, [
            'total_requests' => 0,
            'avg_time' => 0
        ]);

        $metrics[] = [
            'hour' => $hour,
            'requests' => $hourlyMetrics['total_requests'],
            'avg_response_time' => round($hourlyMetrics['avg_time'] * 1000, 2) // ms
        ];
    }

    return $this->sendResponse($metrics, 'API Metrics retrieved successfully');
}
```

---

## 🔒 **تحسينات الأمان**

### **1️⃣ إضافة Input Sanitization:**
```php
// app/Http/Requests/ProductsFilterRequest.php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductsFilterRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'category_id' => 'nullable|integer|exists:categories,id',
            'store_id' => 'nullable|integer|exists:stores,id',
            'brand_id' => 'nullable|integer|exists:brands,id',
            'search_name' => 'nullable|string|max:100|regex:/^[a-zA-Z0-9\s\u0600-\u06FF]+$/',
            'min_price' => 'nullable|numeric|min:0|max:999999',
            'max_price' => 'nullable|numeric|min:0|max:999999|gte:min_price',
            'sort_by' => 'nullable|in:price,created_at,title,rating,popularity',
            'sort_direction' => 'nullable|in:asc,desc',
            'page' => 'nullable|integer|min:1|max:1000',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function messages()
    {
        return [
            'search_name.regex' => 'البحث يجب أن يحتوي على أحرف وأرقام فقط',
            'max_price.gte' => 'الحد الأقصى للسعر يجب أن يكون أكبر من الحد الأدنى',
            'page.max' => 'رقم الصفحة لا يمكن أن يتجاوز 1000',
        ];
    }

    protected function prepareForValidation()
    {
        // تنظيف البيانات قبل التحقق
        if ($this->has('search_name')) {
            $this->merge([
                'search_name' => strip_tags(trim($this->search_name))
            ]);
        }
    }
}
```

### **2️⃣ استخدام Request في Controller:**
```php
public function getProducts(ProductsFilterRequest $request)
{
    // البيانات الآن محققة ومنظفة تلقائياً
    $validatedData = $request->validated();

    // باقي منطق الـ API...
}
```

---

## 📚 **التوثيق التلقائي للـ API**

### **1️⃣ إضافة API Documentation:**
```php
/**
 * @OA\Get(
 *     path="/api/products",
 *     summary="Get products list",
 *     description="Retrieve a paginated list of products with optional filtering",
 *     tags={"Products"},
 *     @OA\Parameter(
 *         name="category_id",
 *         in="query",
 *         description="Filter by category ID",
 *         required=false,
 *         @OA\Schema(type="integer")
 *     ),
 *     @OA\Parameter(
 *         name="search_name",
 *         in="query",
 *         description="Search in product titles",
 *         required=false,
 *         @OA\Schema(type="string", maxLength=100)
 *     ),
 *     @OA\Parameter(
 *         name="min_price",
 *         in="query",
 *         description="Minimum price filter",
 *         required=false,
 *         @OA\Schema(type="number", minimum=0)
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Products retrieved successfully",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="تم جلب المنتجات بنجاح"),
 *             @OA\Property(
 *                 property="data",
 *                 type="object",
 *                 @OA\Property(
 *                     property="product",
 *                     type="array",
 *                     @OA\Items(
 *                         @OA\Property(property="id", type="integer"),
 *                         @OA\Property(property="title", type="string"),
 *                         @OA\Property(property="price", type="number"),
 *                         @OA\Property(property="image_url", type="string"),
 *                         @OA\Property(property="is_favorite", type="boolean")
 *                     )
 *                 ),
 *                 @OA\Property(
 *                     property="pagination",
 *                     type="object",
 *                     @OA\Property(property="current_page", type="integer"),
 *                     @OA\Property(property="total_pages", type="integer"),
 *                     @OA\Property(property="total_items", type="integer")
 *                 )
 *             )
 *         )
 *     )
 * )
 */
public function getProducts(ProductsFilterRequest $request)
{
    // منطق الـ API...
}
```

### **2️⃣ توليد Documentation:**
```bash
# تثبيت swagger
composer require darkaonline/l5-swagger

# توليد التوثيق
php artisan l5-swagger:generate

# عرض التوثيق في المتصفح
# http://localhost:8000/api/documentation
```

---

*هذا المثال الشامل يوضح كيفية التعديل البرمجي المتقدم على APIs مع تطبيق أفضل الممارسات في الأمان والأداء والاختبار.*
