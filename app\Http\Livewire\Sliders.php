<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\Slider;
use App\Traits\Alert;
use App\Traits\PublicFunction;
use Livewire\WithPagination;
class Sliders extends Component
{
    use PublicFunction , Alert ,WithPagination;
    public $columes;
    public $page_length = 10;
    public $search ;
    public $model_title="";
    public $sortBy="created_at";
    public $sortDirection="desc";
    protected $listeners = ['governorate-livewire:conformDelete' => 'conformDelete'];
    protected $paginationTheme = 'bootstrap';
    public function mount()
    {

            $this->columes =Slider::getColumnLang();

        $this->model_title=\Lang::get('lang.add_slider');

        $this->page_length = request()->query('page_length',$this->page_length);
        $this->search = request()->query('search',$this->search);
    }
    public function render()
    {

        $data = Slider::query();
        if($this->search){
            $search = $this->search;
            $data->where('link','LIKE',$search);
        }
        $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);


        if(in_array('sliders_show',$this->actions_permission()) ) {
            return view('dashboard.sliders.index',[ 'data'=>$data])->extends('dashboard_layout.main');
        }else{

            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }
    }

    public function edit($id){
        return redirect()->route('dashboard.sliders.edit',$id);
    }


    public function delete($id){
        $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'governorate-livewire:conformDelete',['id'=>$id]);
    }
    public function conformDelete($id){

        Slider::find($id['id'])->delete();

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

    }


    public function sortBy($field){
        if($this->sortDirection =="desc"){
            $this->sortDirection='asc';
        }else{
            $this->sortDirection='desc';
        }

        return $this->sortBy = $field;
    }

}
