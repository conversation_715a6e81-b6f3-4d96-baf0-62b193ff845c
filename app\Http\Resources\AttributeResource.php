<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AttributeResource extends JsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */

    public function toArray($request)
    {


        $res =  [
            "id"=>$this->id,
            "title"=>$this->title,
            "type"=>$this->type,
            "value"=>json_decode($this->pivot->value),
            "value_string"=>$this->getValueString(),
            "is_required"=>(bool)$this->is_required,
            "options"=>$this->getOptionsWithSelectedFlag(AttributeOptionsResource::collection($this->attribute_options)),
        ];

        if ($this->type !="dropdown" && $this->type != "multiselect" )
            $res["options"] = [] ;

        return $res ;

    }

    public function getValueString()
    {
        switch ($this->type){

            case "multiselect"  :
            case "dropdown"  :
                return $this->getOptionsAsValue();
            case "range"  :
                return $this->getRangeAsValue();
            default:
                return $this->pivot->value && $this->pivot->value!="null"?$this->pivot->value:"" ;

        }

    }

    private function getOptionsAsValue()
    {

        $out = [] ;

        $value = json_decode($this->pivot->value);
        $values = []  ;

        if (!is_array($value)){
            $values[] = $value ;
        }else
            $values = $value ;
        //dd($this->attribute_options);
        foreach ($this->attribute_options as $option){

            foreach ($values as $attr_id){
            //    dd($attr_id ,$option->id);
                if ($attr_id == $option->id)
                    $out[] = $option->title;
            }
        }

        return implode(",",$out) ;

    }
    private function getRangeAsValue()
    {


        $value = json_decode($this->pivot->value);

        if (is_array($value) && count($value)== 2){
           return $value[0]." - ".$value[1];
        }else
            return  $value ;

    }

    private function getOptionsWithSelectedFlag( $options)
    {

        $out = [] ;

        $value = json_decode($this->pivot->value);
        $values = []  ;

        if (!is_array($value)){
            $values[] = $value ;
        }else
            $values = $value ;

        $i = 0 ;

        foreach ($options as $option  ){

            $options[$i]["selected"] = in_array($options[$i]["id"],$values);

            foreach ($values as $attr_id){
                //    dd($attr_id ,$option->id);
                if ($attr_id == $option->id)
                    $out[] = $option->title;
            }

            $i++;
        }


        return $options;

    }

}
