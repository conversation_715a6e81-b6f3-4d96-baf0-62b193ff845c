<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\SubCategory;
class SubCategoryLivewire extends Component
{
    use PublicFunction , Alert,WithPagination,WithFileUploads;
    use Alert;
    public $columes;
    public $category=['image_url'=>''];
    public $image;
    public $type =0;
    public $page_length = 10;
    public $search ,$user_id;
    public $model_title="";
    public $sortBy="created_at";
    public $sortDirection="desc";
    protected $paginationTheme = 'bootstrap';
    protected $listeners = ['sub-category-livewire:conformDelete' => 'conformDelete'];
    public $searchable;
    public  $search_array=[];
    public function mount()
    {
        $searchable = SubCategory::$searchable;
        $this->searchable =collect($searchable);
        $this->columes =SubCategory::getColumnLang();
        $this->columes['belong_to'][2]=true;

        $this->model_title='اضافة تصنيف';

        $this->page_length = request()->query('page_length',$this->page_length);
        $this->search = request()->query('search',$this->search);
    }

    public function render()
    {

        $data = SubCategory::search($this->search_array);
//        if($this->search){
//            $this->resetPage();
//            $search = $this->search;
//            $data->where('name','LIKE','%'.$search.'%');
//        }
        $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);


        if(in_array('categories_show',$this->actions_permission()) ) {
            return view('dashboard.categories.sub_category',['data'=>$data])->extends('dashboard_layout.main');
        }else{

            return view('dashboard.not-authorized')->extends('dashboard_layout.main');
        }
    }

    public function add(){
        $this->category =[
            'name'=>'',
            'image'=>'',
            'image_url'=>'',
            'status'=>true
        ];
        $this->showDataModel('basic','show');
    }

    public function save(){

        $this->validate([
                'category.name' => 'required',
                'category.main_category_id' => 'required',
            ]
        );
        $this->showDataModel('basic','hide');
        $filename = $this->image?$this->image->store('/','public'):null;
        $this->category['image']=$filename;
        $create = SubCategory::create($this->category);
        $this->showDataModel('basic','hide');
        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
    }

    public  function edit($id){
        $this->image=null;
        $this->category  =SubCategory::find($id)->toArray();
        $this->model_title=\Lang::get('lang.edit');
        $this->showDataModel('basic','show');
    }

    public  function update($id){
        $object = SubCategory::find($id);

        $this->validate([
                'category.name' => 'required',

                'category.main_category_id' => 'required',
            ]
        );
        $this->showDataModel('basic','hide');
        $filename = $this->image?$this->image->store('/','public'):null;
        $this->category['image']=$filename;
        $object->update($this->category);

    }

    public function delete($id){
        $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'sub-category-livewire:conformDelete',['id'=>$id]);
    }

    public function conformDelete($id){
        $category = SubCategory::find($id['id']);
//        if($category->products->isNotEmpty()){
//            $this->showModal('لا يمكن الحذف','لا يمكن حذف التصنيف  بسبب وجود منتجات مرتبطة بها','error');
//            return  true;
//        }
        if($category->children->isNotEmpty()){
            $this->showModal('لا يمكن الحذف','لا يمكن حذف التصنيف  بسبب وجود تصنيفات  مرتبطة بها','error');
            return  true;
        }
        SubCategory::find($id['id'])->delete();
       // dd('k');
        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

    }

    public function sortBy($field){
        if($this->sortDirection =="desc"){
            $this->sortDirection='asc';
        }else{
            $this->sortDirection='desc';
        }

        return $this->sortBy = $field;
    }
    public function setStatus($id){
        $object = SubCategory::find($id);
        $object->status =!$object->status;
        $object->save();
    }
    public function resetSearch(){
        $this->search_array=[];
    }



    public function search(){
        $this->resetPage();
    }

    public function updatingPageLength(){
        $this->resetPage();
    }
}

