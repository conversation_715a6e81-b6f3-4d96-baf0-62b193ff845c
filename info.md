# 📋 دليل شامل لهيكل مشروع Laravel API

## 🗂️ **1. المجلدات الرئيسية ووظائفها**

### **📁 app** - قلب التطبيق
هذا هو المجلد الأهم الذي يحتوي على منطق التطبيق:

```
app/
├── Http/Controllers/    # Controllers للتحكم في منطق API
├── Models/             # نماذج قاعدة البيانات (Eloquent Models)
├── Services/           # خدمات مخصصة لمنطق العمل
├── Middleware/         # وسطاء للتحكم في الطلبات
├── Providers/          # مقدمي الخدمات
├── Exceptions/         # معالجة الأخطاء المخصصة
└── Traits/            # خصائص قابلة للإعادة الاستخدام
```

### **📁 routes** - تعريف المسارات
```
routes/
├── api.php      # مسارات API (الأهم في مشروعك)
├── web.php      # مسارات الويب (أقل أهمية في API)
├── console.php  # أوامر Artisan المخصصة
└── channels.php # قنوات البث المباشر
```

### **📁 config** - ملفات التكوين
يحتوي على جميع إعدادات التطبيق:

```
config/
├── app.php        # إعدادات التطبيق الأساسية
├── database.php   # إعدادات قاعدة البيانات
├── auth.php       # إعدادات المصادقة
├── cors.php       # إعدادات CORS للـ API
├── sanctum.php    # إعدادات Laravel Sanctum للمصادقة
└── services.php   # إعدادات الخدمات الخارجية
```

### **📁 database** - قاعدة البيانات
```
database/
├── migrations/  # ملفات الهجرة لإنشاء الجداول
├── seeders/     # بيانات أولية للجداول
└── factories/   # مصانع البيانات للاختبار
```

### **📁 public** - الملفات العامة
```
public/
├── index.php    # نقطة دخول التطبيق
├── css/         # ملفات CSS
├── js/          # ملفات JavaScript
└── images/      # الصور والملفات المرفوعة
```

### **📁 storage** - التخزين
```
storage/
├── app/         # ملفات التطبيق المرفوعة
├── framework/   # ملفات Laravel المؤقتة
└── logs/        # ملفات السجلات
```

### **📁 resources** - الموارد
```
resources/
├── views/       # قوالب Blade (غير مهم في API)
├── lang/        # ملفات الترجمة
└── js/css/      # ملفات المصدر للواجهة
```

### **📁 bootstrap** - التهيئة الأولية
```
bootstrap/
├── app.php      # تهيئة التطبيق
└── cache/       # ملفات التخزين المؤقت للتهيئة
```

### **📁 vendor** - مكتبات Composer
```
vendor/          # مكتبات PHP المثبتة عبر Composer
└── [لا تعدل هذا المجلد يدوياً]
```

---

## 🎯 **2. أهمية مجلد Controllers في مشروع API**

```php
// مثال من مشروعك - HomeApiController.php
class HomeApiController extends BaseControllerApi
{
    public function index(Request $request) {
        // منطق جلب البيانات
        $products = Product::isAllActive()->get();
        
        // إرجاع استجابة JSON
        return $this->sendResponse($products, 'تم جلب البيانات بنجاح');
    }
}
```

**Controllers تقوم بـ:**
- 📥 استقبال طلبات HTTP
- 🔄 معالجة البيانات والمنطق
- 📤 إرجاع استجابات JSON
- 🛡️ التحقق من صحة البيانات
- 🔗 التفاعل مع Models

**تنظيم Controllers في مشروعك:**
```
app/Http/Controllers/
├── Api/                    # Controllers خاصة بـ API
│   ├── AuthApiController.php
│   ├── HomeApiController.php
│   ├── UserApiController.php
│   └── ProductController.php
├── Store/                  # Controllers خاصة بالمتاجر
│   ├── StoreAuthApiController.php
│   └── StoreProductApiController.php
└── BaseControllerApi.php   # Controller أساسي للـ API
```

---

## 🛣️ **3. تنظيم Routes في api.php**

```php
// مثال من مشروعك
Route::group(['middleware' => ['api.language']], function(){
    
    // مسارات عامة (بدون مصادقة)
    Route::get('/home', [HomeApiController::class, 'index']);
    Route::post('/login', [AuthApiController::class, 'login']);
    
    // مسارات محمية (تتطلب مصادقة)
    Route::group(['middleware' => ['auth:sanctum'], 'prefix' => 'auth'], function () {
        Route::get('/profile', [UserApiController::class, 'getProfile']);
        Route::post('/logout', [AuthApiController::class, 'logout']);
    });
    
    // مسارات المتاجر
    Route::group(['prefix' => 'store'], function () {
        Route::post('/register', [StoreAuthApiController::class, 'register']);
        Route::post('/login', [StoreAuthApiController::class, 'login']);
        
        Route::group(['middleware' => ['auth:store'], 'prefix' => 'auth'], function () {
            Route::get('/profile', [StoreAuthApiController::class, 'getProfile']);
            Route::get('/products', [StoreProductApiController::class, 'index']);
        });
    });
});
```

**تنظيم المسارات:**
- 🌐 **Route Groups**: تجميع المسارات ذات الخصائص المشتركة
- 🔒 **Middleware Groups**: تطبيق وسطاء على مجموعة مسارات
- 📝 **Prefix**: إضافة بادئة للمسارات (`/api/store/`, `/api/auth/`)
- 🎯 **Resource Routes**: مسارات CRUD تلقائية
- 🔐 **Multiple Guards**: استخدام guards مختلفة (`auth:sanctum`, `auth:store`, `auth:customers`)

---

## 🛡️ **4. دور Middleware وأماكن تخزينها**

```php
// مثال من مشروعك - ApiLanguage.php
class ApiLanguage
{
    public function handle(Request $request, Closure $next)
    {
        // تحديد لغة التطبيق من Header
        if ($request->header('X-Client-Language')) {
            App::setLocale($request->header('X-Client-Language'));
        } else {
            App::setLocale('ar');
        }
        
        // حفظ اللغة في بيانات المستخدم
        $guards = ["customers","store"];
        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                Auth::guard($guard)->user()->lang_code = App::getLocale();
                Auth::guard($guard)->user()->save();
            }
        }
        
        return $next($request);
    }
}
```

**Middleware مخزن في:**
- 📁 `app/Http/Middleware/` - الوسطاء المخصصة
- ⚙️ `app/Http/Kernel.php` - تسجيل الوسطاء

**Middleware في مشروعك:**
```
app/Http/Middleware/
├── ApiLanguage.php          # تحديد لغة API
├── Authenticate.php         # التحقق من المصادقة
├── RequestLog.php           # تسجيل الطلبات
├── TelescopeTokenMiddleware.php  # حماية Telescope
└── [وسطاء أخرى]
```

**وظائف Middleware:**
- 🔐 المصادقة والتفويض
- 🌍 تحديد اللغة (كما في مشروعك)
- 📊 تسجيل الطلبات
- 🛡️ الحماية من CSRF
- ⏱️ Rate Limiting
- 🔍 التحقق من الصلاحيات

---

## ⚙️ **5. وظيفة ملفات config/*.php**

```php
// مثال من config/app.php في مشروعك
return [
    'name' => env('APP_NAME', 'Laravel'),
    'env' => env('APP_ENV', 'production'),
    'debug' => (bool) env('APP_DEBUG', false),
    'url' => env('APP_URL', 'http://localhost'),
    'locale' => 'ar',                           # اللغة الافتراضية
    'supported_locales' => ['en','ar','he','tr'], # اللغات المدعومة
    'fallback_locale' => 'ar',                  # اللغة الاحتياطية
    'faker_locale' => 'ar_JO',                  # لغة البيانات الوهمية
    // إعدادات مخصصة للصور
    'def_thumb_width' => 150,
    'def_thumb_height' => 150,
    'def_img_width' => 500,
    'def_img_height' => 500,
];
```

**ملفات التكوين المهمة في مشروعك:**
- 🔧 `app.php` - إعدادات التطبيق العامة واللغات
- 🗄️ `database.php` - اتصالات قاعدة البيانات
- 🔐 `auth.php` - أنظمة المصادقة (customers, store, sanctum)
- 🌐 `cors.php` - إعدادات CORS للـ API
- 📧 `mail.php` - إعدادات البريد الإلكتروني
- 🔒 `sanctum.php` - إعدادات Laravel Sanctum
- 🎯 `permission.php` - إعدادات الصلاحيات
- 🌍 `languages.php` - إعدادات اللغات المخصصة

---

## 🏗️ **6. أماكن Services والRepositories**

في مشروعك الحالي:

```
app/Services/           # خدمات مخصصة موجودة
├── SendNotification.php
└── [خدمات أخرى]

// يمكن إضافة:
app/Repositories/       # مستودعات البيانات (يُنصح بإضافتها)
├── ProductRepository.php
├── UserRepository.php
├── StoreRepository.php
└── [repositories أخرى]

app/Actions/           # إجراءات محددة (موجود في مشروعك)
└── Statistics.php
```

**أفضل الممارسات:**
- 📁 `app/Services/` - منطق العمل المعقد (إرسال الإشعارات، معالجة الدفع)
- 📁 `app/Repositories/` - طبقة الوصول للبيانات (استعلامات معقدة)
- 📁 `app/Actions/` - إجراءات محددة (إحصائيات، تقارير)
- 📁 `app/Utils/` - أدوات مساعدة (موجود في مشروعك)

**مثال على Service:**
```php
// app/Services/ProductService.php
class ProductService
{
    public function getActiveProducts($filters = [])
    {
        return Product::isAllActive()
            ->when($filters['category'] ?? null, function($query, $category) {
                $query->where('category_id', $category);
            })
            ->paginate(15);
    }
}
```

---

## 🚫 **7. هل يمكن تجاهل resources/views؟**

**نعم، يمكن تجاهلها في مشروع API خالص!**

```
resources/views/    # غير ضروري في API خالص
├── welcome.blade.php
├── layouts/
└── components/

// بدلاً من ذلك، ركز على:
app/Http/Resources/     # تحويل البيانات لـ JSON (موجود في مشروعك)
├── ProductResource.php
├── UserResource.php
├── StoreResource.php
├── CategoryResource.php
└── [resources أخرى]
```

**لكن احتفظ بـ:**
- 📧 قوالب البريد الإلكتروني (إذا كنت ترسل emails)
- 📄 صفحات الأخطاء البسيطة
- 📋 وثائق API (يمكن استخدام Blade لها)

**API Resources في مشروعك:**
```php
// مثال من ProductResource.php
class ProductResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'price' => $this->price,
            'image_url' => $this->image_url,
            'is_favorite' => $this->is_favorite,
            'store' => new StoreResource($this->store),
        ];
    }
}
```

---

## 🔄 **8. الفرق بين web.php و api.php**

| **web.php** | **api.php** |
|-------------|-------------|
| 🌐 مسارات الويب التقليدية | 🔗 مسارات API |
| 🍪 يدعم Sessions & Cookies | 🎫 يدعم Token Authentication |
| 🛡️ حماية CSRF | 🚫 بدون حماية CSRF |
| 📄 يرجع HTML/Views | 📊 يرجع JSON |
| 🔗 `/route` | 🔗 `/api/route` |
| 🎯 للمتصفحات | 🎯 للتطبيقات المحمولة/SPA |

```php
// api.php - مسارات API (مشروعك يركز على هذا)
Route::get('/products', [ProductController::class, 'index']);
// النتيجة: /api/products

// web.php - مسارات الويب (أقل استخداماً في مشروعك)
Route::get('/dashboard', [DashboardController::class, 'index']);
// النتيجة: /dashboard
```

**في مشروعك:**
- ✅ `api.php` - يحتوي على جميع endpoints الرئيسية
- ⚠️ `web.php` - يحتوي على مسارات إدارية قليلة

---

## 💡 **9. مثال بسيط على Route و Controller و Response**

### **1️⃣ تعريف Route:**
```php
// في routes/api.php
Route::group(['middleware' => ['api.language']], function(){
    // مسارات عامة
    Route::get('/products', [ProductController::class, 'index']);
    Route::get('/products/{id}', [ProductController::class, 'show']);
    
    // مسارات محمية
    Route::group(['middleware' => ['auth:sanctum']], function () {
        Route::post('/products', [ProductController::class, 'store']);
        Route::put('/products/{id}', [ProductController::class, 'update']);
        Route::delete('/products/{id}', [ProductController::class, 'destroy']);
    });
});
```

### **2️⃣ إنشاء Controller:**
```php
<?php
// app/Http/Controllers/Api/ProductController.php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseControllerApi;
use App\Models\Product;
use App\Http\Resources\ProductResource;
use Illuminate\Http\Request;

class ProductController extends BaseControllerApi
{
    public function index(Request $request)
    {
        $products = Product::isAllActive()
            ->when($request->category_id, function($query, $categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->paginate(15);
        
        return $this->sendResponse([
            'products' => ProductResource::collection($products),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'total_pages' => $products->lastPage(),
                'total_items' => $products->total(),
            ]
        ], 'تم جلب المنتجات بنجاح');
    }
    
    public function show($id)
    {
        $product = Product::isAllActive()->findOrFail($id);
        
        return $this->sendResponse(
            new ProductResource($product),
            'تم جلب المنتج بنجاح'
        );
    }
    
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id'
        ]);
        
        $product = Product::create([
            'title' => $request->title,
            'price' => $request->price,
            'category_id' => $request->category_id,
            'store_id' => auth('sanctum')->user()->store_id,
        ]);
        
        return $this->sendResponse(
            new ProductResource($product),
            'تم إنشاء المنتج بنجاح',
            201
        );
    }
}
```

### **3️⃣ استجابة JSON (باستخدام BaseControllerApi):**
```json
{
    "success": true,
    "message": "تم جلب المنتجات بنجاح",
    "data": {
        "products": [
            {
                "id": 1,
                "title": "منتج تجريبي",
                "price": 100,
                "image_url": "https://example.com/storage/product/image.jpg",
                "category": {
                    "id": 1,
                    "name": "إلكترونيات"
                },
                "store": {
                    "id": 1,
                    "name": "متجر تجريبي"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_items": 67
        }
    }
}
```

---

## 🎯 **خلاصة أفضل الممارسات لمشروع Laravel API:**

### **🏗️ البنية والتنظيم:**
1. **📁 تنظيم الملفات**: استخدم مجلدات منطقية للـ Controllers والServices
2. **🔄 فصل المسؤوليات**: Controllers للتحكم، Services للمنطق، Models للبيانات
3. **📊 API Resources**: استخدمها لتنسيق البيانات بشكل موحد
4. **🎯 Route Groups**: جمع المسارات المترابطة

### **🛡️ الأمان والحماية:**
5. **🔐 المصادقة**: استخدم Sanctum مع Guards متعددة (كما في مشروعك)
6. **🛡️ Middleware**: طبق الحماية والتحقق على المسارات
7. **✅ التحقق**: استخدم Form Requests للتحقق من البيانات
8. **🌐 CORS**: اضبط إعدادات CORS بشكل صحيح

### **⚡ الأداء والجودة:**
9. **💾 التخزين المؤقت**: استخدم Cache للبيانات المتكررة
10. **📄 Pagination**: قسم النتائج الكبيرة
11. **🔍 الاستعلامات**: تجنب N+1 Problem باستخدام `with()`
12. **📝 التوثيق**: وثق API endpoints بوضوح

### **🧪 الاختبار والصيانة:**
13. **🧪 الاختبارات**: اكتب اختبارات للـ API endpoints
14. **📊 المراقبة**: استخدم أدوات مثل Telescope (موجود في مشروعك)
15. **🗂️ إدارة الأخطاء**: معالجة موحدة للأخطاء
16. **🌍 اللغات المتعددة**: دعم متعدد اللغات (كما في مشروعك)

---

## 📚 **موارد إضافية:**

- 📖 [Laravel Documentation](https://laravel.com/docs)
- 🔐 [Laravel Sanctum](https://laravel.com/docs/sanctum)
- 📊 [API Resources](https://laravel.com/docs/eloquent-resources)
- 🧪 [Testing APIs](https://laravel.com/docs/http-tests)
- 🛡️ [Security Best Practices](https://laravel.com/docs/security)

---

## 🔧 **مثال عملي: تطوير API المنتجات**

### **📍 API الحالي في مشروعك:**

```php
// routes/api.php - المسارات الموجودة
Route::get('/products', [HomeApiController::class, 'getProducts']);
Route::get('/products/search', [HomeApiController::class, 'searchProducts']);
Route::get('/productsRandom', [HomeApiController::class, 'getProductsRandom']);
Route::get('/relatedProducts', [HomeApiController::class, 'relatedProducts']);

// مسارات المتاجر
Route::group(['middleware' => ['auth:store'], 'prefix' => 'store/auth'], function () {
    Route::get('/products', [StoreProductApiController::class, 'index']);
    Route::post('/products/save', [StoreProductApiController::class, 'store']);
    Route::post('/products/update', [StoreProductApiController::class, 'update']);
    Route::post('/products/delete', [StoreProductApiController::class, 'delete']);
});
```

### **🎯 كيفية التعديل على API المنتجات:**

#### **1️⃣ إضافة مسار جديد (Route):**

```php
// في routes/api.php - إضافة مسار للمنتجات المميزة
Route::get('/products/featured', [HomeApiController::class, 'getFeaturedProducts']);

// إضافة مسار للمنتجات حسب العلامة التجارية
Route::get('/products/brand/{brand_id}', [HomeApiController::class, 'getProductsByBrand']);

// إضافة مسار لتقييم المنتج
Route::group(['middleware' => ['auth:customers']], function () {
    Route::post('/products/{id}/rate', [ProductController::class, 'rateProduct']);
    Route::post('/products/{id}/favorite', [ProductController::class, 'toggleFavorite']);
});
```

#### **2️⃣ إنشاء Controller Method جديد:**

```php
// في app/Http/Controllers/Api/HomeApiController.php
public function getFeaturedProducts(Request $request)
{
    $cacheKey = 'featured_products_' . ($request->page ?? 1);

    $result = Cache::remember($cacheKey, 600, function () use ($request) {
        $products = Product::isAllActive()
            ->where('is_featured', true)  // عمود جديد للمنتجات المميزة
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return [
            'products' => ProductHomeResource::collection($products),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'total_pages' => $products->lastPage(),
                'total_items' => $products->total(),
                'per_page' => $products->perPage(),
            ]
        ];
    });

    return $this->sendResponse($result, 'تم جلب المنتجات المميزة بنجاح');
}

public function getProductsByBrand($brandId, Request $request)
{
    $request->validate([
        'brand_id' => 'exists:brands,id'
    ]);

    $products = Product::isAllActive()
        ->where('brand_id', $brandId)
        ->when($request->sort_by, function($query, $sortBy) {
            switch($sortBy) {
                case 'price_low':
                    return $query->orderBy('price', 'asc');
                case 'price_high':
                    return $query->orderBy('price', 'desc');
                case 'newest':
                    return $query->orderBy('created_at', 'desc');
                default:
                    return $query->orderBy('created_at', 'desc');
            }
        })
        ->paginate(15);

    $result = [
        'products' => ProductHomeResource::collection($products),
        'brand' => $products->first()?->brand,
        'pagination' => [
            'current_page' => $products->currentPage(),
            'total_pages' => $products->lastPage(),
            'total_items' => $products->total(),
        ]
    ];

    return $this->sendResponse($result, 'تم جلب منتجات العلامة التجارية بنجاح');
}
```

#### **3️⃣ تحسين API موجود:**

```php
// تحسين getProducts الموجود
public function getProducts(Request $request)
{
    // إضافة validation
    $request->validate([
        'category_id' => 'nullable|exists:categories,id',
        'store_id' => 'nullable|exists:stores,id',
        'min_price' => 'nullable|numeric|min:0',
        'max_price' => 'nullable|numeric|min:0',
        'sort_by' => 'nullable|in:price_low,price_high,newest,popular',
        'per_page' => 'nullable|integer|min:1|max:50'
    ]);

    // تحسين الـ caching
    $cacheKey = $this->generateCacheKey($request);

    $result = Cache::remember($cacheKey, 300, function () use ($request) {
        $query = Product::isAllActive();

        // تطبيق الفلاتر
        $this->applyFilters($query, $request);

        // تطبيق الترتيب
        $this->applySorting($query, $request);

        $products = $query->paginate($request->per_page ?? 15);

        return [
            'products' => ProductHomeResource::collection($products),
            'filters_applied' => $this->getAppliedFilters($request),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'total_pages' => $products->lastPage(),
                'total_items' => $products->total(),
                'per_page' => $products->perPage(),
            ]
        ];
    });

    return $this->sendResponse($result, 'تم جلب المنتجات بنجاح');
}

private function applyFilters($query, $request)
{
    if ($request->category_id) {
        $query->getByCategory($request->category_id);
    }

    if ($request->store_id) {
        $query->where('store_id', $request->store_id);
    }

    if ($request->min_price) {
        $query->where('price', '>=', $request->min_price);
    }

    if ($request->max_price) {
        $query->where('price', '<=', $request->max_price);
    }

    if ($request->search_name) {
        $query->onProductTitle($request->search_name);
    }

    if ($request->is_offer) {
        $query->whereNotNull('new_price');
    }

    return $query;
}

private function applySorting($query, $request)
{
    switch($request->sort_by) {
        case 'price_low':
            $query->orderBy('price', 'asc');
            break;
        case 'price_high':
            $query->orderBy('price', 'desc');
            break;
        case 'newest':
            $query->orderBy('created_at', 'desc');
            break;
        case 'popular':
            $query->withCount('favorite')->orderBy('favorite_count', 'desc');
            break;
        default:
            $query->inRandomOrder();
    }

    return $query;
}
```

#### **4️⃣ تحسين ProductResource:**

```php
// في app/Http/Resources/ProductResource.php
public function toArray($request)
{
    return [
        'id' => $this->id,
        'title' => $this->title,
        'slug' => Str::slug($this->title), // إضافة slug
        'price' => getRound($this->price),
        'formatted_price' => number_format($this->price, 2) . ' ' . $this->currency->symbol,
        'is_available' => $this->stock_quantity > 0,
        'stock_quantity' => $this->stock_quantity,
        'description' => $this->description,
        'short_description' => Str::limit($this->description, 100), // وصف مختصر

        // معلومات التصنيف والعلامة التجارية
        'category' => new CategoryResource($this->category),
        'brand' => new BrandResource($this->brand),

        // معلومات العروض
        'is_offer' => $this->offer_id && $this->new_price,
        'original_price' => getRound($this->price),
        'sale_price' => getRound($this->new_price),
        'discount_percentage' => $this->offer_discount_rate,

        // معلومات العملة
        'currency' => new CurrencyResource($this->currency),
        'charged_price' => getRound(getPriceForUser($this->price, $this->currency)),
        'charged_currency' => new CurrencyResource(getCurrentUserCurrency()),

        // الصور
        'image_url' => $this->getOptimizedImageUrl($request),
        'gallery' => $this->product_images->map(function($image) {
            return [
                'id' => $image->id,
                'url' => url('image/600/400/' . $image->image),
                'is_main' => $image->is_main,
            ];
        }),

        // معلومات التفاعل
        'is_favorite' => $this->isFavoriteForCurrentUser(),
        'favorites_count' => $this->favorite_count ?? $this->favorite()->count(),
        'reviews_count' => $this->reviews_count ?? $this->reviews()->count(),
        'average_rating' => round($this->reviews()->avg('rating') ?? 0, 1),

        // معلومات إضافية
        'is_new' => $this->is_new,
        'is_featured' => $this->is_featured ?? false,
        'tags' => $this->tags ? explode(',', $this->tags) : [],
        'created_at' => $this->created_at,
        'updated_at' => $this->updated_at,

        // معلومات المتجر
        'store' => new StoreResource($this->store),

        // الألوان والأحجام
        'variations' => $this->getFormattedVariations(),
        'available_colors' => $this->getAvailableColors(),
        'available_sizes' => $this->getAvailableSizes(),
    ];
}

private function getOptimizedImageUrl($request)
{
    $size = $request->image_size ?? 'medium';

    $dimensions = [
        'small' => '300/200',
        'medium' => '600/400',
        'large' => '1200/800'
    ];

    $dimension = $dimensions[$size] ?? $dimensions['medium'];

    return url("image/{$dimension}/" . $this->file_image);
}

private function isFavoriteForCurrentUser()
{
    if (!auth('customers')->check()) {
        return false;
    }

    return $this->favorite()
        ->where('customer_id', auth('customers')->id())
        ->exists();
}
```

#### **5️⃣ إضافة Validation مخصص:**

```php
// إنشاء app/Http/Requests/ProductFilterRequest.php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductFilterRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'category_id' => 'nullable|exists:categories,id',
            'store_id' => 'nullable|exists:stores,id',
            'brand_id' => 'nullable|exists:brands,id',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0|gte:min_price',
            'search_name' => 'nullable|string|max:255',
            'sort_by' => 'nullable|in:price_low,price_high,newest,popular,rating',
            'per_page' => 'nullable|integer|min:1|max:50',
            'is_offer' => 'nullable|boolean',
            'is_new' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
        ];
    }

    public function messages()
    {
        return [
            'category_id.exists' => 'التصنيف المحدد غير موجود',
            'store_id.exists' => 'المتجر المحدد غير موجود',
            'max_price.gte' => 'الحد الأقصى للسعر يجب أن يكون أكبر من الحد الأدنى',
            'per_page.max' => 'لا يمكن عرض أكثر من 50 منتج في الصفحة الواحدة',
        ];
    }
}

// استخدام الـ Request في Controller
public function getProducts(ProductFilterRequest $request)
{
    // البيانات مُتحقق منها تلقائياً
    $validatedData = $request->validated();

    // باقي الكود...
}
```

#### **6️⃣ إضافة Middleware مخصص:**

```php
// إنشاء app/Http/Middleware/ProductAccess.php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ProductAccess
{
    public function handle(Request $request, Closure $next)
    {
        // تسجيل عدد مرات الوصول للمنتج
        if ($request->route('id')) {
            $productId = $request->route('id');

            // زيادة عداد المشاهدات
            \DB::table('products')
                ->where('id', $productId)
                ->increment('views_count');

            // تسجيل في الإحصائيات
            \DB::table('product_views')->insert([
                'product_id' => $productId,
                'user_id' => auth('customers')->id(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'created_at' => now(),
            ]);
        }

        return $next($request);
    }
}

// تسجيل الـ Middleware في app/Http/Kernel.php
protected $routeMiddleware = [
    // ...
    'product.access' => \App\Http\Middleware\ProductAccess::class,
];

// استخدامه في المسارات
Route::get('/products/{id}', [ProductController::class, 'show'])
    ->middleware('product.access');
```

#### **7️⃣ إضافة Service للمنطق المعقد:**

```php
// إنشاء app/Services/ProductService.php
<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\Cache;

class ProductService
{
    public function getRecommendedProducts($userId, $limit = 10)
    {
        $cacheKey = "recommended_products_user_{$userId}";

        return Cache::remember($cacheKey, 3600, function () use ($userId, $limit) {
            // منطق التوصيات المعقد
            $userFavorites = \DB::table('favorites')
                ->where('customer_id', $userId)
                ->pluck('product_id');

            if ($userFavorites->isEmpty()) {
                return Product::isAllActive()
                    ->inRandomOrder()
                    ->limit($limit)
                    ->get();
            }

            // جلب منتجات مشابهة بناءً على التصنيفات المفضلة
            $favoriteCategories = Product::whereIn('id', $userFavorites)
                ->pluck('category_id')
                ->unique();

            return Product::isAllActive()
                ->whereIn('category_id', $favoriteCategories)
                ->whereNotIn('id', $userFavorites)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
        });
    }

    public function updateProductStock($productId, $quantity)
    {
        $product = Product::findOrFail($productId);

        if ($product->stock_quantity < $quantity) {
            throw new \Exception('الكمية المطلوبة غير متوفرة');
        }

        $product->decrement('stock_quantity', $quantity);

        // إرسال تنبيه إذا انخفض المخزون
        if ($product->stock_quantity <= $product->low_stock_threshold) {
            // إرسال إشعار للمتجر
            $this->notifyLowStock($product);
        }

        return $product;
    }

    private function notifyLowStock($product)
    {
        // منطق إرسال الإشعارات
    }
}

// استخدام الـ Service في Controller
public function getRecommendations(Request $request)
{
    $userId = auth('customers')->id();
    $productService = new ProductService();

    $recommendations = $productService->getRecommendedProducts($userId, 12);

    return $this->sendResponse([
        'products' => ProductHomeResource::collection($recommendations)
    ], 'تم جلب التوصيات بنجاح');
}
```

### **🚀 خطوات التطبيق العملي:**

1. **📝 خطط للتغيير**: حدد ما تريد إضافته أو تعديله
2. **🧪 اختبر محلياً**: تأكد من عمل التعديلات
3. **📊 راقب الأداء**: استخدم Cache و Database Indexing
4. **🔒 أمّن API**: طبق Validation و Rate Limiting
5. **📖 وثّق التغييرات**: حدث الوثائق والتعليقات

---

*هذا الدليل مبني على هيكل مشروعك الفعلي ويمكن تطويره حسب احتياجاتك المستقبلية.*
