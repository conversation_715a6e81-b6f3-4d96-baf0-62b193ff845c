# 📋 دليل شامل لهيكل مشروع Laravel API

## 🗂️ **1. المجلدات الرئيسية ووظائفها**

### **📁 app** - قلب التطبيق
هذا هو المجلد الأهم الذي يحتوي على منطق التطبيق:

```
app/
├── Http/Controllers/    # Controllers للتحكم في منطق API
├── Models/             # نماذج قاعدة البيانات (Eloquent Models)
├── Services/           # خدمات مخصصة لمنطق العمل
├── Middleware/         # وسطاء للتحكم في الطلبات
├── Providers/          # مقدمي الخدمات
├── Exceptions/         # معالجة الأخطاء المخصصة
└── Traits/            # خصائص قابلة للإعادة الاستخدام
```

### **📁 routes** - تعريف المسارات
```
routes/
├── api.php      # مسارات API (الأهم في مشروعك)
├── web.php      # مسارات الويب (أقل أهمية في API)
├── console.php  # أوامر Artisan المخصصة
└── channels.php # قنوات البث المباشر
```

### **📁 config** - ملفات التكوين
يحتوي على جميع إعدادات التطبيق:

```
config/
├── app.php        # إعدادات التطبيق الأساسية
├── database.php   # إعدادات قاعدة البيانات
├── auth.php       # إعدادات المصادقة
├── cors.php       # إعدادات CORS للـ API
├── sanctum.php    # إعدادات Laravel Sanctum للمصادقة
└── services.php   # إعدادات الخدمات الخارجية
```

### **📁 database** - قاعدة البيانات
```
database/
├── migrations/  # ملفات الهجرة لإنشاء الجداول
├── seeders/     # بيانات أولية للجداول
└── factories/   # مصانع البيانات للاختبار
```

### **📁 public** - الملفات العامة
```
public/
├── index.php    # نقطة دخول التطبيق
├── css/         # ملفات CSS
├── js/          # ملفات JavaScript
└── images/      # الصور والملفات المرفوعة
```

### **📁 storage** - التخزين
```
storage/
├── app/         # ملفات التطبيق المرفوعة
├── framework/   # ملفات Laravel المؤقتة
└── logs/        # ملفات السجلات
```

### **📁 resources** - الموارد
```
resources/
├── views/       # قوالب Blade (غير مهم في API)
├── lang/        # ملفات الترجمة
└── js/css/      # ملفات المصدر للواجهة
```

### **📁 bootstrap** - التهيئة الأولية
```
bootstrap/
├── app.php      # تهيئة التطبيق
└── cache/       # ملفات التخزين المؤقت للتهيئة
```

### **📁 vendor** - مكتبات Composer
```
vendor/          # مكتبات PHP المثبتة عبر Composer
└── [لا تعدل هذا المجلد يدوياً]
```

---

## 🎯 **2. أهمية مجلد Controllers في مشروع API**

```php
// مثال من مشروعك - HomeApiController.php
class HomeApiController extends BaseControllerApi
{
    public function index(Request $request) {
        // منطق جلب البيانات
        $products = Product::isAllActive()->get();
        
        // إرجاع استجابة JSON
        return $this->sendResponse($products, 'تم جلب البيانات بنجاح');
    }
}
```

**Controllers تقوم بـ:**
- 📥 استقبال طلبات HTTP
- 🔄 معالجة البيانات والمنطق
- 📤 إرجاع استجابات JSON
- 🛡️ التحقق من صحة البيانات
- 🔗 التفاعل مع Models

**تنظيم Controllers في مشروعك:**
```
app/Http/Controllers/
├── Api/                    # Controllers خاصة بـ API
│   ├── AuthApiController.php
│   ├── HomeApiController.php
│   ├── UserApiController.php
│   └── ProductController.php
├── Store/                  # Controllers خاصة بالمتاجر
│   ├── StoreAuthApiController.php
│   └── StoreProductApiController.php
└── BaseControllerApi.php   # Controller أساسي للـ API
```

---

## 🛣️ **3. تنظيم Routes في api.php**

```php
// مثال من مشروعك
Route::group(['middleware' => ['api.language']], function(){
    
    // مسارات عامة (بدون مصادقة)
    Route::get('/home', [HomeApiController::class, 'index']);
    Route::post('/login', [AuthApiController::class, 'login']);
    
    // مسارات محمية (تتطلب مصادقة)
    Route::group(['middleware' => ['auth:sanctum'], 'prefix' => 'auth'], function () {
        Route::get('/profile', [UserApiController::class, 'getProfile']);
        Route::post('/logout', [AuthApiController::class, 'logout']);
    });
    
    // مسارات المتاجر
    Route::group(['prefix' => 'store'], function () {
        Route::post('/register', [StoreAuthApiController::class, 'register']);
        Route::post('/login', [StoreAuthApiController::class, 'login']);
        
        Route::group(['middleware' => ['auth:store'], 'prefix' => 'auth'], function () {
            Route::get('/profile', [StoreAuthApiController::class, 'getProfile']);
            Route::get('/products', [StoreProductApiController::class, 'index']);
        });
    });
});
```

**تنظيم المسارات:**
- 🌐 **Route Groups**: تجميع المسارات ذات الخصائص المشتركة
- 🔒 **Middleware Groups**: تطبيق وسطاء على مجموعة مسارات
- 📝 **Prefix**: إضافة بادئة للمسارات (`/api/store/`, `/api/auth/`)
- 🎯 **Resource Routes**: مسارات CRUD تلقائية
- 🔐 **Multiple Guards**: استخدام guards مختلفة (`auth:sanctum`, `auth:store`, `auth:customers`)

---

## 🛡️ **4. دور Middleware وأماكن تخزينها**

```php
// مثال من مشروعك - ApiLanguage.php
class ApiLanguage
{
    public function handle(Request $request, Closure $next)
    {
        // تحديد لغة التطبيق من Header
        if ($request->header('X-Client-Language')) {
            App::setLocale($request->header('X-Client-Language'));
        } else {
            App::setLocale('ar');
        }
        
        // حفظ اللغة في بيانات المستخدم
        $guards = ["customers","store"];
        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                Auth::guard($guard)->user()->lang_code = App::getLocale();
                Auth::guard($guard)->user()->save();
            }
        }
        
        return $next($request);
    }
}
```

**Middleware مخزن في:**
- 📁 `app/Http/Middleware/` - الوسطاء المخصصة
- ⚙️ `app/Http/Kernel.php` - تسجيل الوسطاء

**Middleware في مشروعك:**
```
app/Http/Middleware/
├── ApiLanguage.php          # تحديد لغة API
├── Authenticate.php         # التحقق من المصادقة
├── RequestLog.php           # تسجيل الطلبات
├── TelescopeTokenMiddleware.php  # حماية Telescope
└── [وسطاء أخرى]
```

**وظائف Middleware:**
- 🔐 المصادقة والتفويض
- 🌍 تحديد اللغة (كما في مشروعك)
- 📊 تسجيل الطلبات
- 🛡️ الحماية من CSRF
- ⏱️ Rate Limiting
- 🔍 التحقق من الصلاحيات

---

## ⚙️ **5. وظيفة ملفات config/*.php**

```php
// مثال من config/app.php في مشروعك
return [
    'name' => env('APP_NAME', 'Laravel'),
    'env' => env('APP_ENV', 'production'),
    'debug' => (bool) env('APP_DEBUG', false),
    'url' => env('APP_URL', 'http://localhost'),
    'locale' => 'ar',                           # اللغة الافتراضية
    'supported_locales' => ['en','ar','he','tr'], # اللغات المدعومة
    'fallback_locale' => 'ar',                  # اللغة الاحتياطية
    'faker_locale' => 'ar_JO',                  # لغة البيانات الوهمية
    // إعدادات مخصصة للصور
    'def_thumb_width' => 150,
    'def_thumb_height' => 150,
    'def_img_width' => 500,
    'def_img_height' => 500,
];
```

**ملفات التكوين المهمة في مشروعك:**
- 🔧 `app.php` - إعدادات التطبيق العامة واللغات
- 🗄️ `database.php` - اتصالات قاعدة البيانات
- 🔐 `auth.php` - أنظمة المصادقة (customers, store, sanctum)
- 🌐 `cors.php` - إعدادات CORS للـ API
- 📧 `mail.php` - إعدادات البريد الإلكتروني
- 🔒 `sanctum.php` - إعدادات Laravel Sanctum
- 🎯 `permission.php` - إعدادات الصلاحيات
- 🌍 `languages.php` - إعدادات اللغات المخصصة

---

## 🏗️ **6. أماكن Services والRepositories**

في مشروعك الحالي:

```
app/Services/           # خدمات مخصصة موجودة
├── SendNotification.php
└── [خدمات أخرى]

// يمكن إضافة:
app/Repositories/       # مستودعات البيانات (يُنصح بإضافتها)
├── ProductRepository.php
├── UserRepository.php
├── StoreRepository.php
└── [repositories أخرى]

app/Actions/           # إجراءات محددة (موجود في مشروعك)
└── Statistics.php
```

**أفضل الممارسات:**
- 📁 `app/Services/` - منطق العمل المعقد (إرسال الإشعارات، معالجة الدفع)
- 📁 `app/Repositories/` - طبقة الوصول للبيانات (استعلامات معقدة)
- 📁 `app/Actions/` - إجراءات محددة (إحصائيات، تقارير)
- 📁 `app/Utils/` - أدوات مساعدة (موجود في مشروعك)

**مثال على Service:**
```php
// app/Services/ProductService.php
class ProductService
{
    public function getActiveProducts($filters = [])
    {
        return Product::isAllActive()
            ->when($filters['category'] ?? null, function($query, $category) {
                $query->where('category_id', $category);
            })
            ->paginate(15);
    }
}
```

---

## 🚫 **7. هل يمكن تجاهل resources/views؟**

**نعم، يمكن تجاهلها في مشروع API خالص!**

```
resources/views/    # غير ضروري في API خالص
├── welcome.blade.php
├── layouts/
└── components/

// بدلاً من ذلك، ركز على:
app/Http/Resources/     # تحويل البيانات لـ JSON (موجود في مشروعك)
├── ProductResource.php
├── UserResource.php
├── StoreResource.php
├── CategoryResource.php
└── [resources أخرى]
```

**لكن احتفظ بـ:**
- 📧 قوالب البريد الإلكتروني (إذا كنت ترسل emails)
- 📄 صفحات الأخطاء البسيطة
- 📋 وثائق API (يمكن استخدام Blade لها)

**API Resources في مشروعك:**
```php
// مثال من ProductResource.php
class ProductResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'price' => $this->price,
            'image_url' => $this->image_url,
            'is_favorite' => $this->is_favorite,
            'store' => new StoreResource($this->store),
        ];
    }
}
```

---

## 🔄 **8. الفرق بين web.php و api.php**

| **web.php** | **api.php** |
|-------------|-------------|
| 🌐 مسارات الويب التقليدية | 🔗 مسارات API |
| 🍪 يدعم Sessions & Cookies | 🎫 يدعم Token Authentication |
| 🛡️ حماية CSRF | 🚫 بدون حماية CSRF |
| 📄 يرجع HTML/Views | 📊 يرجع JSON |
| 🔗 `/route` | 🔗 `/api/route` |
| 🎯 للمتصفحات | 🎯 للتطبيقات المحمولة/SPA |

```php
// api.php - مسارات API (مشروعك يركز على هذا)
Route::get('/products', [ProductController::class, 'index']);
// النتيجة: /api/products

// web.php - مسارات الويب (أقل استخداماً في مشروعك)
Route::get('/dashboard', [DashboardController::class, 'index']);
// النتيجة: /dashboard
```

**في مشروعك:**
- ✅ `api.php` - يحتوي على جميع endpoints الرئيسية
- ⚠️ `web.php` - يحتوي على مسارات إدارية قليلة

---

## 💡 **9. مثال بسيط على Route و Controller و Response**

### **1️⃣ تعريف Route:**
```php
// في routes/api.php
Route::group(['middleware' => ['api.language']], function(){
    // مسارات عامة
    Route::get('/products', [ProductController::class, 'index']);
    Route::get('/products/{id}', [ProductController::class, 'show']);
    
    // مسارات محمية
    Route::group(['middleware' => ['auth:sanctum']], function () {
        Route::post('/products', [ProductController::class, 'store']);
        Route::put('/products/{id}', [ProductController::class, 'update']);
        Route::delete('/products/{id}', [ProductController::class, 'destroy']);
    });
});
```

### **2️⃣ إنشاء Controller:**
```php
<?php
// app/Http/Controllers/Api/ProductController.php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseControllerApi;
use App\Models\Product;
use App\Http\Resources\ProductResource;
use Illuminate\Http\Request;

class ProductController extends BaseControllerApi
{
    public function index(Request $request)
    {
        $products = Product::isAllActive()
            ->when($request->category_id, function($query, $categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->paginate(15);
        
        return $this->sendResponse([
            'products' => ProductResource::collection($products),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'total_pages' => $products->lastPage(),
                'total_items' => $products->total(),
            ]
        ], 'تم جلب المنتجات بنجاح');
    }
    
    public function show($id)
    {
        $product = Product::isAllActive()->findOrFail($id);
        
        return $this->sendResponse(
            new ProductResource($product),
            'تم جلب المنتج بنجاح'
        );
    }
    
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id'
        ]);
        
        $product = Product::create([
            'title' => $request->title,
            'price' => $request->price,
            'category_id' => $request->category_id,
            'store_id' => auth('sanctum')->user()->store_id,
        ]);
        
        return $this->sendResponse(
            new ProductResource($product),
            'تم إنشاء المنتج بنجاح',
            201
        );
    }
}
```

### **3️⃣ استجابة JSON (باستخدام BaseControllerApi):**
```json
{
    "success": true,
    "message": "تم جلب المنتجات بنجاح",
    "data": {
        "products": [
            {
                "id": 1,
                "title": "منتج تجريبي",
                "price": 100,
                "image_url": "https://example.com/storage/product/image.jpg",
                "category": {
                    "id": 1,
                    "name": "إلكترونيات"
                },
                "store": {
                    "id": 1,
                    "name": "متجر تجريبي"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_items": 67
        }
    }
}
```

---

## 🎯 **خلاصة أفضل الممارسات لمشروع Laravel API:**

### **🏗️ البنية والتنظيم:**
1. **📁 تنظيم الملفات**: استخدم مجلدات منطقية للـ Controllers والServices
2. **🔄 فصل المسؤوليات**: Controllers للتحكم، Services للمنطق، Models للبيانات
3. **📊 API Resources**: استخدمها لتنسيق البيانات بشكل موحد
4. **🎯 Route Groups**: جمع المسارات المترابطة

### **🛡️ الأمان والحماية:**
5. **🔐 المصادقة**: استخدم Sanctum مع Guards متعددة (كما في مشروعك)
6. **🛡️ Middleware**: طبق الحماية والتحقق على المسارات
7. **✅ التحقق**: استخدم Form Requests للتحقق من البيانات
8. **🌐 CORS**: اضبط إعدادات CORS بشكل صحيح

### **⚡ الأداء والجودة:**
9. **💾 التخزين المؤقت**: استخدم Cache للبيانات المتكررة
10. **📄 Pagination**: قسم النتائج الكبيرة
11. **🔍 الاستعلامات**: تجنب N+1 Problem باستخدام `with()`
12. **📝 التوثيق**: وثق API endpoints بوضوح

### **🧪 الاختبار والصيانة:**
13. **🧪 الاختبارات**: اكتب اختبارات للـ API endpoints
14. **📊 المراقبة**: استخدم أدوات مثل Telescope (موجود في مشروعك)
15. **🗂️ إدارة الأخطاء**: معالجة موحدة للأخطاء
16. **🌍 اللغات المتعددة**: دعم متعدد اللغات (كما في مشروعك)

---

## 📚 **موارد إضافية:**

- 📖 [Laravel Documentation](https://laravel.com/docs)
- 🔐 [Laravel Sanctum](https://laravel.com/docs/sanctum)
- 📊 [API Resources](https://laravel.com/docs/eloquent-resources)
- 🧪 [Testing APIs](https://laravel.com/docs/http-tests)
- 🛡️ [Security Best Practices](https://laravel.com/docs/security)

---

*هذا الدليل مبني على هيكل مشروعك الفعلي ويمكن تطويره حسب احتياجاتك المستقبلية.*
