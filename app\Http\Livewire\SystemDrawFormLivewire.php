<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\SystemDraw;
class SystemDrawFormLivewire extends Component
{
    use WithFileUploads,Alert,PublicFunction;
    public $title = "";
    public $file_name = 'systemDraw';
    public $systemDraw;
    public $sponsors=[];
    protected $listeners = ['systemDraw-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
    protected $rules = [
        "systemDraw.title"=>'required',
        "systemDraw.prize_name"=>'required',
        "systemDraw.date"=>'required',
        "systemDraw.winer_id"=>'nullable',
        "systemDraw.winer_type"=>'nullable',
        "systemDraw.sponsor_id"=>'nullable',
        "systemDraw.store_id"=>'nullable',
        "systemDraw.status"=>'nullable',
    ];

    protected $validationAttributes;
    public function __construct($id = null)
    {
        parent::__construct($id);
        $this->validationAttributes = $this->getColNameForValidation(SystemDraw::getColumnLang());
    }
    public function mount($id =null)
    {
        $this->title = \Lang::get('lang.add_account')  ;
        $this->systemDraw  = $id?SystemDraw::find($id):new SystemDraw();
        $this->sponsors = $this->systemDraw->sponsors->isEmpty()?[]
            :
            $this->systemDraw->sponsors->pluck('id')->toArray();
        // dd($this->systemDraw->sponsors->pluck('id')->toArray());
    }
    public function render()
    {
        return view('dashboard/systemDraw/form')->extends('dashboard_layout.main');
    }

    public function save(){
        $this->validate();
        \DB::beginTransaction();
        try {
            $this->systemDraw->store_id =   auth()->user()->type == "STORE"?auth()->user()->store->id:null;
            $this->systemDraw->save();
            \DB::commit();
            $this->systemDraw->sponsors()->sync($this->sponsors);
            $this->showModal(
                \Lang::get('lang.saved_done'),
                \Lang::get('lang.saved_changed'),
                'success');
            return redirect()->route('dashboard.systemDraw');
        } catch (\Exception $e) {
            \DB::rollback();
            $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
        }
    }

}


