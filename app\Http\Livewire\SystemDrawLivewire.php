<?php

namespace App\Http\Livewire;

use App\Models\Store;
use App\Models\User;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\SystemDraw;
class SystemDrawLivewire extends Component
{
    use WithFileUploads,Alert,PublicFunction,WithPagination;
    public $columes;
    public $searchable;
    public $page_length = 10;
    public $sortBy="created_at";
    public $sortDirection="desc";
    protected $paginationTheme = 'bootstrap';
    public  $search_array=[];
    protected $listeners = ['SystemDraw-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
    public $file_name = 'systemDraw';
    public function mount()
    {
        $searchable = SystemDraw::getSearchable();
        $this->searchable =collect($searchable);
        $this->columes =SystemDraw::getColumnLang();
        $this->searchable =SystemDraw::getSearchable();
        $this->page_length = request()->query('page_length',$this->page_length);
    }
    public function render()
    {
        if (auth()->user()->type=="STORE"){
            $this->search_array["store_id"]=auth()->user()->store->id;
        }
        $data =SystemDraw::search($this->search_array);
        $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);
        return view('dashboard/systemDraw/index',[ 'data'=>$data])->extends('dashboard_layout.main');

    }
    public function search(){
        $this->resetPage();
    }
    public function resetSearch(){
        $this->search_array=[];
    }
    public function edit($id){
        return redirect()->route('dashboard.systemDraw.edit',$id);
    }
    public function delete($id){
        $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'SystemDraw-livewire:conformDelete',['id'=>$id]);
    }
    public function conformDelete($id){

        SystemDraw::find($id['id'])->delete();

        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

    }
    public function setStatus($id){
        $object = SystemDraw::find($id);
        $object->status =!$object->status;
        $object->save();
    }

}

