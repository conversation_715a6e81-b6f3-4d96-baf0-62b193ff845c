<?php

namespace App\Http\Resources;

// use App\Models\Favorite;
// use App\Models\Follower;
// use App\Models\ProductRequest;
// use App\Models\Reviews;
use App\Models\Store;
// use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class AddAdsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {


    // $get_same_product = $this->getMatchingRequestsAndNotify(false);


        // $created_at =   Carbon::parse($this->created_at)->format('Y/m/d');
          $store = Store::find($this->customer_id);
        return [
            "id"=>$this->id,

            // "image"=>$this->user_image?$this->user_image_url:$this->work_image_url,

            // "user_image_url"=>$this->user_image?$this->user_image_url:null,
            // "work_image_url"=>$this->work_image?$this->work_image_url:null,

            // "age"=>$this->type=="offer"?($this->offer_age_from." - ".$this->offer_age_to):$this->age,
            "title"=>$this->title,
            "description"=>$this->Description,
              "customer_id"=>$this->customer_id,
              "city"=>$this->city,
            "country"=>$this->country,
            "media"=>$this->media,
              "store"=>new StoreResource($store),
            // "gender_name"=>$this->gender_name,
            // "contact_email"=>$this->contact_email,
            // "contact_number"=>$this->contact_number,

            // "exp_years"=>$this->type=="offer"?$this->offer_exp_years_from." - ".$this->offer_exp_years_to:$this->exp_years,
            // "offer_exp_years_from"=>$this->offer_exp_years_from,
            // "offer_exp_years_to"=>$this->offer_exp_years_to,

            // "job_type"=>new GeneralResource($this->job_type),

          
            // "customer"=>new CustomerResource($this->customer),
            // "store_id"=>$this->store_id,
            // "store"=>new CustomerResource($this->customer_id),

            // "type"=>$this->type,
            // "type_name"=>$this->type_name,

            // "salary"=>$this->salary,
            // "currency_id"=>$this->currency_id,
            // "currency"=>new CurrencyResource($this->currency),

            // "similarـrequest_count"=>count($get_same_product),
            // "created_at"=>$created_at,
            // "similarـrequests" => count($get_same_product)>0?JobRequestSimilarResource::collection($get_same_product):[],

            // 'country'=>new AreaResource($this->country),
            // 'governorate'=>new AreaResource($this->governorate),
            // 'city'=>new AreaResource($this->city),
            // 'region'=>new AreaResource($this->region),
            // "full_address"=> $this->full_address,

            // 'target_country'=>new AreaResource($this->target_country),
            // 'target_governorate'=>new AreaResource($this->target_governorate),
            // 'target_city'=>new AreaResource($this->target_city),
            // 'target_region'=>new AreaResource($this->target_region),
            // "target_full_address"=> $this->target_full_address


        ];

    }
}
