<?php

namespace App\Http\Livewire;

use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Hash;
use App\Traits\Alert;
class Profile extends Component
{
    use WithFileUploads,Alert;

    public $user=[
        'name'=>'',
        'email'=>'',
        'avatar'=>'',
        'avatar_url'=>'',
    ];

    public $password;
    public $password_confirmation;
    public $password_tab=false;
    public $avatar;
    public $currentUser ;
    public $store ;
    public $image;
    public $days = [
        "السبت",
        "الاحد",
        "الاثنين",
        "الثلاثاء",
        "الاربعاء",
        "الخميس",
        "الجمعة"
    ];

    protected function rules()
    {
        $rules = [
            "store.name"=>'required',
            "store.bio"=>'nullable',
            "store.logo"=>'nullable',
            "store.mobile"=>'required|digits:10|regex:/(05)[0-9]{8}/|unique:stores,mobile,'.$this->store->id,
            "store.phone"=>'nullable',
            "store.email"=>'required|unique:stores,email,'.$this->store->id,
            "store.address"=>'required',
            "store.category_id"=>'required',
            "store.open_time_from"=>'required',
            "store.open_time_to"=>'required',
            "store.status"=>'nullable',
            "store.open_days"=>'nullable',

        ];
        if (is_null($this->store->id)){
            $rules["password"] = 'required|confirmed';
        }

        return $rules ;
    }
    public function mount()
    {
        $this->user=[
            'name'=>auth()->user()->name,
            'email'=>auth()->user()->email,
            'avatar_url'=>auth()->user()->avatarUrl()
        ];

        $this->currentUser = auth()->user();
        $this->store = checkIfUserStore()?getCurrentStoreUser():$this->currentUser;
        $this->days = checkIfUserStore()?json_decode($this->store->open_days):$this->days;

    }

    public function render()
    {
        if (checkIfUserStore()){

            return view('store_dashboard.profile',["store"=>$this->store])->extends('dashboard_layout.main');
        }
        return view('dashboard.profile',[ 'user'=>$this->user ])->extends('dashboard_layout.main');

    }
    public function save(){
        if (checkIfUserStore()){
            return $this->storeUpdate();
        }

        return  $this->userUpdate();


    }


    public function storeUpdate(){

        $this->validate();
        \DB::beginTransaction();
        try {
            $filename = $this->image?$this->image->store('/','public'):$this->store->logo;
            $this->store->logo=$filename;

            if ($this->password){
                $this->store->password = Hash::make($this->password);

            }
            $this->store->status = $this->store->status ? true:false;
            $this->store->open_days = json_encode($this->days);
            $this->store->save();
            $this->updateOnUserInside();
            \DB::commit();
            $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
        } catch (\Exception $e) {
            \DB::rollback();
            $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
        }


    }
    public function userUpdate(){
        $this->validate([
                'user.email' => 'required|email|unique:users,email,'.auth()->id(),
                'user.name' => 'required',
            ]
        );

        $filename = $this->avatar?$this->avatar->store('/','avatars'):$this->currentUser->avatar;

        $user = User::find(auth()->id());
        $user->email = $this->user['email'];
        $user->name = $this->user['name'];
        $user->avatar = $filename;
        if($this->password){
            $user->password=Hash::make($this->password);
        }
        $user->save();


        $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
    }

    public function updateOnUserInside(){
        $user = User::find(auth()->id());
        $user->email = $this->store->email;
        $user->name = $this->store->name;
        if($this->password){
            $user->password=Hash::make($this->password);
        }
        $user->save();
    }

}
