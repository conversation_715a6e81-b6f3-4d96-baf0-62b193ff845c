<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Wallet extends Model
{
    use HasFactory;
    protected $fillable = [
        'store_id',
        'full_name',
        'address',
        'bank_name',
        'account_number',
        'iban_number',
    ];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
}
