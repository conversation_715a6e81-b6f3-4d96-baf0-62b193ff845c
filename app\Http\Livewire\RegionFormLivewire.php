<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Region;
class RegionFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'region';
      public $region;
      protected $listeners = ['Region-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
       protected $rules = [
                "region.name"=>'required',
                "region.name.ar"=>'required',
                "region.name.en"=>'required',
                "region.name.tr"=>'required',
                "region.name.he"=>'required',
                "region.status"=>'nullable',
                "region.olivery_area_code"=>'nullable',
                "region.city_id"=>'required',

       ];
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->region  = $id?Region::find($id):new Region();
          }
      public function render()
          {

              if(in_array('region_create',$this->actions_permission()) ){
                  return view('dashboard/region/form')->extends('dashboard_layout.main');
              }else{
                  return view('dashboard.not-authorized')->extends('dashboard_layout.main');
              }

          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               $this->region->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.region');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


