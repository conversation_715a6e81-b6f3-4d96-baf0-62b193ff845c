<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'=>$this->id,
            'city'=>$this->city,
            'country'=>new AreaResource($this->country),
            "state"=> $this->state,
            "zipcode"=> $this->zipcode,
            "addressline1"=> $this->addressline1,
            "addressline2"=> $this->addressline2,
            "fullname"=> $this->fullname,
            "address"=> $this->address,
            "phone"=> $this->phone,
            "name"=> $this->name,
            "is_default"=> $this->is_default,
            "full_address"=> $this->full_address,
        ];//parent::toArray($request);
    }
}
