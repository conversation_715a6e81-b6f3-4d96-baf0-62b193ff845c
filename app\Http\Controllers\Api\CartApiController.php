<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\BaseControllerApi;
use App\Http\Controllers\Controller;

use App\Http\Resources\CartItemResource;
use App\Http\Resources\CurrencyResource;
use App\Http\Resources\ProductStoreResource;
use App\Jobs\SendToStorjanJob;
use App\Models\Address;
use App\Models\CartApi;
use App\Models\Coupon;
use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Product;
use App\Models\Setting;
use App\Models\Store;
use App\Models\User;
use App\Models\Variation;
use App\Services\CandidateTable;
use App\Services\Delivery;
use App\Services\OrderTransaction;
use App\Services\SendNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Bus;
class CartApiController extends BaseControllerApi
{

    public  function add(Request $request){
        $this->validate($request,[
            'product_id'=>'required',
         //   'variation_id'=>'required',
            'qty'=>'required',
        ],[],[

            'product_id'=>\Lang::get("lang.product"),
            'qty'=>\Lang::get("lang.qty"),
           // 'variation_id'=>'رقم الوحدة',
        ]);


       $product=Product::findOrFail($request->product_id);
        $variation=Variation::find($request->variation_id);

        if(!$this->checkIfAllowToAddInCart($product)){
            return $this->sendError(\Lang::get("lang.can_not_order_from_many_at_the_same_time"),422);
        }
       /* if(! $variation->is_available){
            return $this->sendError('هذا المنتج غير متوفر ',422);
        }*/
        //dd($variation);

        \DB::beginTransaction();


        try {


            $getProductInCartOrNot = CartApi::where('product_id',$product->id)
                ->where('customer_id',$this->CURRENT_USER->id)
                ->where('variation_id',optional($variation)->id)
                ->first();
            if($getProductInCartOrNot){
                $getProductInCartOrNot->update(['qty'=>$request->qty+$getProductInCartOrNot->qty]);
            }else{

                    $cart_create = CartApi::create([
                        'product_id'=>$product->id,
                        'variation_id'=>optional($variation)->id,
                        'customer_id'=>$this->CURRENT_USER->id,
                        'store_id'=>$product->store_id,
                        'qty'=>$request->qty,
                    ]);
            }

            \DB::commit();
            return $this->carts($request);
        } catch (\Exception $e) {
            //$e->getMessage()
            \DB::rollback();

            return $this->sendError($e->getMessage(),500);
        }
    }

    public function update(Request $request){


        \DB::beginTransaction();

        try {
        foreach ($request->carts as $cart){

            $findCart = CartApi::findOrFail($cart['cart_id']);
            $findCart->update([
                'qty'=>$cart['qty']
            ]);

        }
            \DB::commit();
            return $this->carts($request);
        } catch (\Exception $e) {
            //$e->getMessage()
            \DB::rollback();

            return $this->sendError($e->getMessage(),500);
        }
    }

    public function remove(Request $request){
        $this->validate($request,[
            'cart_id'=>'required'
        ]);

        $cart_item = CartApi::where('id',$request->cart_id)->first();
        //dd($cart_item);
        if($cart_item){
            $cart_item->delete();
            return $this->carts($request);
        }else{
            return $this->sendError(\Lang::get("lang.invalid_cart_item"),422);
        }
       //


    }

    public function remove_all(Request $request){
        CartApi::where('customer_id',$this->CURRENT_USER->id)->delete();
        return $this->carts($request);
    }

    public function carts(Request $request,$getJustCouponStatus=null){

      //  $this->checkProductStatusIsAvailable();
        $carts= CartApi::has('product')->where('customer_id',$this->CURRENT_USER->id)->get();
        //doesnthave;
        //has
        $delivery = new Delivery() ;

        $delivery_cost = 0 ;
        $product = 0 ;
        $store = null;



        $total = 0;
        foreach ($carts as $cart){

            $product = Product::find($cart->product_id);

            $variation=Variation::find($cart->variation_id);
            $rate = getRateBaseOnProductType($product->product_type);
            //dd(round($variation->price_after_offer));

            if ($variation){
            if($variation->price_after_offer){
                $total += getPriceForUser(($variation->price_after_offer) * $cart->qty , $product->currency );
            }else{
                $total += getPriceForUser(($variation->display_selling_price) *$cart->qty, $product->currency );
            }

            }else{

                if($product->offer_id&& ($product->new_price?true:false)){
                    $total += getPriceForUser(($product->new_price) * $cart->qty, $product->currency );;
                }else{
                    $total += getPriceForUser(($product->price) *$cart->qty, $product->currency );;
                }

            }

            $store = $product->store;

}


        $coupon_code = Coupon::where('coupon_code',$request->coupon_code)->isActive()->first();

        if($request->coupon_code){

            if(is_null($coupon_code)){

                return $this->sendError( \Lang::get("lang.invalid_coupon"),422);

            }

            if ($coupon_code->min_value && $coupon_code->max_value ){

                if (!($coupon_code->min_value < $total && $total <= $coupon_code->max_value)){
                    $msg = " لا يمكن تنفيذ الكابون يجب ان يكون مجموع  الطلبية اقل من $coupon_code->max_value  واكثر من $coupon_code->min_value   ";
                    return $this->sendError($msg,422);
                }
            }

        }

        $calculateCoupon=0;
        $sub_total=$total;

        $coupon_value=0;
        $total = 0;
        if($coupon_code){
            $calculateCoupon = $this->calculateCoupon($coupon_code,$sub_total);
            $coupon_value=$calculateCoupon['couponAmount'];
            $coupon_code['couponAmount']=$coupon_value;
            $total = numberFormat($calculateCoupon['total'],2);
        }
        if($coupon_code){
            $coupon_object = [
                'id'=>$coupon_code->id,
                'coupon_value'=>$coupon_code->coupon_value,
                'coupon_type'=>$coupon_code->coupon_type,
                'couponAmount'=>numberFormat($coupon_value,2),
            ];
        }
        else{

            $coupon_object=null;
        }

        if($coupon_object){

            if($total < 0 ){
                if($getJustCouponStatus){
                    return false;
                }
                return $this->sendError('لا يمكن تطبيق الكابون لان قيمته أعلى من قيمة الطلب',422);
            }
        }

        if ($request->address_id) {

            $address = Address::where('id',$request->address_id)
                ->where('customer_id',$this->CURRENT_USER->id)->first();
            $delivery_cost =     $delivery->get_delivery_costForUserCurrency($address ,$store );

        }else{
            $address = Address::where('is_default',true)
                ->where('customer_id',$this->CURRENT_USER->id)->first();
            $delivery_cost =     $delivery->get_delivery_costForUserCurrency($address ,$store );
        }

        $total = getRound(($total?($total):($sub_total)) + $delivery_cost) ;

        $final_order = [
          'total'=>[
              'sub_total'=>(double)getRound($sub_total),//ceil(numberFormat($sub_total,2)),
              'delivery_cost' => getRound($delivery_cost),
              'coupon'=>(double)$coupon_object,
              'total'=> (double)($total?getRound($total):getRound($sub_total)),
              'currency'=> new CurrencyResource(getCurrentUserCurrency())
              ],
          'items'=>CartItemResource::collection($carts),
        'cart_count'=>CartApi::where('customer_id',$this->CURRENT_USER->id)->get()->sum('qty'),

        ];
        return $this->sendResponse($final_order,'');
    }

    public function bop_send_to_page(Request $request){

        $this->validate($request,['address_id'=>'required']);
        $status = $this->checkProductStatusIsAvailable();

        if ($status['status']>0){
            return $this->sendError($status['message'],422);
        }

        $address = Address::where('id',$request->address_id)
            ->where('customer_id',$this->CURRENT_USER->id)->first();
        if(is_null($address)){
            return $this->sendError(\Lang::get("invalid_address"),422);
        }

        $delivery_cost = 0 ;
        $store= null;




        $carts= CartApi::has('product')
            ->where('customer_id',$this->CURRENT_USER->id)
            ->get();

        $total = 0;
        foreach ($carts as $cart){

            $product= Product::find($cart->product_id);
            $variation= Variation::find($cart->variation_id);
            $rate = getRateBaseOnProductType($product->product_type);
            //dd(round($variation->price_after_offer));
            if ($variation){

                if($variation->price_after_offer){
                    $total += getPriceForUser(($variation->price_after_offer) * $cart->qty , $product->currency );;

                }else{
                    $total += getPriceForUser(($variation->display_selling_price) *$cart->qty, $product->currency );
                }

            }else{

                if($product->is_offer){
                    $total += getPriceForUser(($product->new_price) * $cart->qty, $product->currency );
                }else{
                    $total += getPriceForUser(($product->price) *$cart->qty, $product->currency );
                }

            }
            $store = $product->store;
        }

        $coupon_code = Coupon::where('coupon_code',$request->coupon_code)->isActive()->first();

        if($request->coupon_code){
            if(is_null($coupon_code)){
                return $this->sendError('الكابون الذي ادخلته غير موجود ',422);
            }
            if ($coupon_code->min_value && $coupon_code->max_value ){

                if (!($coupon_code->min_value < $total && $total <= $coupon_code->max_value)){
                    $msg = " لا يمكن تنفيذ الكابون يجب ان يكون مجموع  الطلبية اقل من $coupon_code->max_value  واكثر من $coupon_code->min_value   ";
                    return $this->sendError($msg,422);
                }
            }
        }


        $calculateCoupon=0;
        $sub_total=$total;
        $coupon_value=0;
        $total = 0;
        if($coupon_code){
            $calculateCoupon = $this->calculateCoupon($coupon_code,$sub_total);
            $coupon_value=$calculateCoupon['couponAmount'];
            $coupon_code['couponAmount']=$coupon_value;
            $total = numberFormat($calculateCoupon['total'],2);
        }
        if($coupon_code){
            $coupon_object = [
                'id'=>$coupon_code->id,
                'coupon_value'=>$coupon_code->coupon_value,
                'coupon_type'=>$coupon_code->coupon_type,
                'couponAmount'=>numberFormat($coupon_value,2),
            ];
        }
        else{

            $coupon_object=null;
        }

        if($coupon_object){

            if($total < 0 ){
                return $this->sendError('لا يمكن تطبيق الكابون لان قيمته أعلى من قيمة الطلب',422);
            }
        }


        $delivery = new Delivery();


        $delivery_cost =     $delivery->get_delivery_costForUserCurrency($address,$store);


        $total = ($total?getRound($total):getRound($sub_total)) + $delivery_cost ;



        $setting = Setting::first();

        $view  =  view("bop",
            [
                "callback" =>route('bop_callback'),
                "total" =>$total?getRound($total):getRound($sub_total),
                "sub_total" =>getRound($sub_total),
                "delivery_cost" =>getRound($delivery_cost),
                "coupon" =>$coupon_object,
                'currency'=> new CurrencyResource(getCurrentUserCurrency()),

                "extra"=>[
                    "customer_id"=>$this->CURRENT_USER->id,
                    "address_id"=>$address->id ,
                    "time_stamp" => microtime(true)
                ],
            ]
        )->render() ;

        if ($request->version == "2") {
            $res["online_payment"] = true;
            if (strtolower($this->CURRENT_USER->device_type) == "ios") {
                if (!$setting->pay_ios_orders) {

                    $res["online_payment"] = false;

                }
            } else {

                if (!$setting->pay_android_orders) {

                    $res["online_payment"] = false;
                }

            }


            $res["web"] = $res["online_payment"] ? $view : "";

            if (!$res["online_payment"]){
                //save for cash on delivery
                $this->saveAfterPaymentComeFromBop($this->CURRENT_USER->id, $address->id);

            }

            return $this->sendResponse($res, \Lang::get("lang.order_sent_successfully"));
        }else{

            return $view ;

        }

    }
    
    
     public function bop_send_to_page_Ads(Request $request){

        // $this->validate($request,['address_id'=>'required']);
        // $status = $this->checkProductStatusIsAvailable();

        // if ($status['status']>0){
        //     return $this->sendError($status['message'],422);
        // }

        // $address = Address::where('id',$request->address_id)
        //     ->where('customer_id',$this->CURRENT_USER->id)->first();
        // if(is_null($address)){
        //     return $this->sendError(\Lang::get("invalid_address"),422);
        // }

        // $delivery_cost = 0 ;
        // $store= null;




        // $carts= CartApi::has('product')
        //     ->where('customer_id',$this->CURRENT_USER->id)
        //     ->get();

        // $total = 0;
        // foreach ($carts as $cart){

        //     $product= Product::find($cart->product_id);
        //     $variation= Variation::find($cart->variation_id);
        //     $rate = getRateBaseOnProductType($product->product_type);
        //     //dd(round($variation->price_after_offer));
        //     if ($variation){

        //         if($variation->price_after_offer){
        //             $total += getPriceForUser(($variation->price_after_offer) * $cart->qty , $product->currency );;

        //         }else{
        //             $total += getPriceForUser(($variation->display_selling_price) *$cart->qty, $product->currency );
        //         }

        //     }else{

        //         if($product->is_offer){
        //             $total += getPriceForUser(($product->new_price) * $cart->qty, $product->currency );
        //         }else{
        //             $total += getPriceForUser(($product->price) *$cart->qty, $product->currency );
        //         }

        //     }
        //     $store = $product->store;
        // }

        // $coupon_code = Coupon::where('coupon_code',$request->coupon_code)->isActive()->first();

        // if($request->coupon_code){
        //     if(is_null($coupon_code)){
        //         return $this->sendError('الكابون الذي ادخلته غير موجود ',422);
        //     }
        //     if ($coupon_code->min_value && $coupon_code->max_value ){

        //         if (!($coupon_code->min_value < $total && $total <= $coupon_code->max_value)){
        //             $msg = " لا يمكن تنفيذ الكابون يجب ان يكون مجموع  الطلبية اقل من $coupon_code->max_value  واكثر من $coupon_code->min_value   ";
        //             return $this->sendError($msg,422);
        //         }
        //     }
        // }


        // $calculateCoupon=0;
        // $sub_total=$total;
        // $coupon_value=0;
        // $total = 0;
        // if($coupon_code){
        //     $calculateCoupon = $this->calculateCoupon($coupon_code,$sub_total);
        //     $coupon_value=$calculateCoupon['couponAmount'];
        //     $coupon_code['couponAmount']=$coupon_value;
        //     $total = numberFormat($calculateCoupon['total'],2);
        // }
        // if($coupon_code){
        //     $coupon_object = [
        //         'id'=>$coupon_code->id,
        //         'coupon_value'=>$coupon_code->coupon_value,
        //         'coupon_type'=>$coupon_code->coupon_type,
        //         'couponAmount'=>numberFormat($coupon_value,2),
        //     ];
        // }
        // else{

        //     $coupon_object=null;
        // }

        // if($coupon_object){

        //     if($total < 0 ){
        //         return $this->sendError('لا يمكن تطبيق الكابون لان قيمته أعلى من قيمة الطلب',422);
        //     }
        // }


        // $delivery = new Delivery();


        // $delivery_cost =     $delivery->get_delivery_costForUserCurrency($address,$store);


        // $total = ($total?getRound($total):getRound($sub_total)) + $delivery_cost ;



        // $setting = Setting::first();

        $view  =  view("bop",
            [
                "callback" =>route('bop_callback'),
                "total" =>100,
                "sub_total" =>20,
                "delivery_cost" =>10,
                "coupon" =>"",
                'currency'=> new CurrencyResource(getCurrentUserCurrency()),

                "extra"=>[
                    "customer_id"=>$this->CURRENT_USER->id,
                    "address_id"=>"test" ,
                    "time_stamp" => microtime(true)
                ],
            ]
        )->render() ;
        
          $res["web"] =  $view ;
           return $view ;

        // if ($request->version == "2") {
        //     $res["online_payment"] = true;
        //     if (strtolower($this->CURRENT_USER->device_type) == "ios") {
        //         if (!$setting->pay_ios_orders) {

        //             $res["online_payment"] = false;

        //         }
        //     } else {

        //         if (!$setting->pay_android_orders) {

        //             $res["online_payment"] = false;
        //         }

        //     }


        //     $res["web"] = $res["online_payment"] ? $view : "";

        //     if (!$res["online_payment"]){
        //         //save for cash on delivery
        //         $this->saveAfterPaymentComeFromBop($this->CURRENT_USER->id, $address->id);

        //     }

        //     return $this->sendResponse($res, \Lang::get("lang.order_sent_successfully"));
        // }else{

        //     return $view ;

        // }

    }
    
    public function save(Request $request,$user_id = null){

        if ($user_id){
            $this->CURRENT_USER = Customer::find($user_id);
        }


        $this->validate($request,[
            'address_id'=>'required'
        ]);

        $status = $this->checkProductStatusIsAvailable();
        if ($status['status']>0){
            return $this->sendError($status['message'],422);
        }
        \DB::beginTransaction();
        $address = Address::where('id',$request->address_id)
            ->where('customer_id',$this->CURRENT_USER->id)->first();
        if(is_null($address)){
            return $this->sendError(\Lang::get("invalid_address"),422);
        }
        try {

            $coupon_code = Coupon::where('coupon_code',$request->coupon_code)->isActive()->first();
            if($request->coupon_code){
                if(is_null($coupon_code)){
                    return $this->sendError('الكابون الذي ادخلته غير صحيح',422);
                }
                elseif ($coupon_code){
                    $coupon_used_in_order_count = Order::where('coupon_id',$coupon_code->id)->where('customer_id',$this->CURRENT_USER->id)->get()->count();
                    if($coupon_code->expired_date < \Carbon\Carbon::today() ){
                        return $this->sendError('الكابون الذي ادخلته غير صحيح',422);
                    }elseif($coupon_used_in_order_count >= $coupon_code->coupon_used){
                        return $this->sendError('لقد تجاوزة الحد المسموح للاستخدام',422);
                    }
                }
                if ($coupon_code->min_value && $coupon_code->max_value ){

                    if (!($coupon_code->min_value < $this->getOrderTotal() && $this->getOrderTotal() <= $coupon_code->max_value)){
                        $msg = " لا يمكن تنفيذ الكابون يجب ان يكون مجموع  الطلبية اقل من $coupon_code->max_value  واكثر من $coupon_code->min_value   ";
                        return $this->sendError($msg,422);
                    }
                }
            }

            $carts= CartApi::where('customer_id',$this->CURRENT_USER->id)->get();


            $order = Order::create([
                'address_id'=>$address->id,
                'customer_id'=>$this->CURRENT_USER->id,
                'coupon_id'=>optional($coupon_code)->id,
                'currency_id'=>optional(getCurrentUserCurrency())->id,
                'status'=>0,
            ]);
            $total =0;
            $store_id =0;

            foreach ($carts as $cart ){
                $product =Product::find($cart->product_id);
                $variation=Variation::find($cart->variation_id);
                $price = getPriceForUser( getProductPrice($product,$variation) , $product->currency );

                //$price = $product->new_price?$product->new_price:$product->price;
                $orderItem = OrderItem::create([
                    'product_id'=>$cart->product_id,
                    'product_object'=>json_encode(Product::find($cart->product_id)),
                    'variation_id'=>$cart->variation_id,
                    'variation_object'=>json_encode(Variation::find($cart->variation_id)),
                    'order_id'=>$order->id,
                    'order'=>json_encode(Order::find($order->id)),
                    'qty'=>$cart->qty,
                    'price'=> $price
                ]);
                $total +=  $price*$cart->qty;
                $store_id = $product->store_id;
            }

            if($coupon_code){
                $calculateCoupon = $this->calculateCoupon($coupon_code,$total);
                $order->coupon_id = $coupon_code->id;
                $order->coupon_value = $calculateCoupon['couponAmount'];
                $order->sub_total = $total;
                $order->total = $calculateCoupon['total'];
                $order->currency_id = optional(getCurrentUserCurrency())->id;

                $order->save();
                $coupon_code->update(['number_of_used'=>$coupon_code->number_of_used+1]);
            }
            else{
                $order->sub_total = $total;
                $order->total = $total;
                $order->currency_id = optional(getCurrentUserCurrency())->id;
                $order->save();
            }


            $order->store_id = $store_id;
            $order->save();

            \DB::commit();
                // send to store jan
            $receiver=Customer::find($this->CURRENT_USER->id);
            $order_item=OrderItem::where('order_id',$order->id)->get();


             //dd($new_products);
            OrderTransaction::save($order,"order");
            CartApi::where('customer_id',$this->CURRENT_USER->id)->delete();


            SendNotification::customerSendOrderToStore($order);

            CandidateTable::save($order,"order");

            return $this->sendResponse(null,\Lang::get("lang.order_sent_successfully"));

        } catch (\Exception $e) {
            \DB::rollback();
            return $this->sendError($e->getMessage(),500);
        }

    }

    public function coupon(Request $request){
        $this->validate($request,[
            'coupon_code'=>'required',

        ],[],[
            'coupon_code'=>'رقم الكابون',
        ]);


        return $this->carts($request);

    }

    public function calculateCoupon($coupon,$total){
        if($coupon->coupon_type === 'percentage'){
            $couponAmount = $total * ($coupon->coupon_value / 100);
            return  ['total'=>$total-$couponAmount,'couponAmount'=>$couponAmount];
        }
        if($coupon->coupon_type === 'fixed'){

            return  ['total'=>$total-$coupon->coupon_value,'couponAmount'=>(double)$coupon->coupon_value];
        }
    }


    public function getOrderTotal(){
        $carts = CartApi::where('customer_id',$this->CURRENT_USER->id)->get();
        $total =0;
        foreach ($carts as $cart ){

            $product =Product::find($cart->product_id);
            $variation=Variation::find($cart->variation_id);
            $price = getPriceForUser( getProductPrice($product,$variation),$product->currency );
            $total +=$price*$cart->qty;

        }
        return $total;

    }


    public function checkProductStatusIsAvailable(){
        $carts = CartApi::where('customer_id',$this->CURRENT_USER->id)->get();
        $product_count_is_unactive=0;
        $massage=\Lang::get("not_available_products");

        foreach ($carts as $cart ){

            $product =Product::where("id",$cart->product_id)->isAllActive()->first();

            if (is_null($product)){
                $massage .="\n";
                $massage .=$cart->product->title;
                $massage .="\n";

                $product_count_is_unactive = $product_count_is_unactive + 1 ;
            }

        }
        return ["message"=>$massage,"status"=>$product_count_is_unactive];
    }

    public function checkIfAllowToAddInCart($product){

        $cart = CartApi::where('customer_id',$this->CURRENT_USER->id)
            ->whereHas("product")->first();
        if($cart){
            $checkforstore =  CartApi::
            where('customer_id',$this->CURRENT_USER->id)
                ->where('store_id',$product->store_id)
                ->whereHas("product")
                ->first();

            return $checkforstore?true:false;
        }

        return true;



    }

    public function bop_callback(Request $request){

        file_put_contents('payment_log_file.log',print_r($request->all(),true).'\n',FILE_APPEND);
        $data = \request()->all();
       // dd($data);


        $order = base64_decode($data['OrderID']);
        $order_data = json_decode($order);

        $ResponseCode = intval($data['ResponseCode']);
        $ReasonCode = intval($data['ReasonCode']);
        $PaymentStatus = $data['ReasonCodeDesc'];


        $payment = Payment::create([
            "customer_id"=>$order_data->customer_id,
            "payment_status"=>$PaymentStatus,
            "payload"=>json_encode($data)
        ]);


        if ( $ResponseCode ===1 && $ReasonCode ===1 ){


            $this->saveAfterPaymentComeFromBop($order_data->customer_id, $order_data->address_id,true);
            return  redirect()->route('checkout.success');

        }

        return  redirect()->route('checkout.fail',["message"=>isset($data['ReasonCodeDesc'])?$data['ReasonCodeDesc']:""]); //$this->sendError("error",406,$data);

    }



    public function saveAfterPaymentComeFromBop($customer_id,$address_id , $paid_online = false){

        try {

            $carts= CartApi::where('customer_id',$customer_id)->get();

            $order = Order::create([
                'address_id'=>$address_id,
                'customer_id'=>$customer_id,
                'coupon_id'=>null,
                'status'=>0,
                'is_payment'=>$paid_online,
                'payment_method'=>$paid_online?"Visa":"COD",
                'currency_id'=>optional(getCurrentUserCurrency())->id,

            ]);

            $total =0;
            $store_id =0;

            foreach ($carts as $cart ){
                $product =Product::find($cart->product_id);
                $variation=Variation::find($cart->variation_id);
                $price = getPriceForUser( getProductPrice($product,$variation),$product->currency );

                //$price = $product->new_price?$product->new_price:$product->price;
                $orderItem = OrderItem::create([
                    'product_id'=>$cart->product_id,
                    'product_object'=>json_encode(Product::find($cart->product_id)),
                    'variation_id'=>$cart->variation_id,
                    'variation_object'=>json_encode(Variation::find($cart->variation_id)),
                    'order_id'=>$order->id,
                    'order'=>json_encode(Order::find($order->id)),
                    'qty'=>$cart->qty,
                    'price'=> $price
                ]);
                $total +=  $price*$cart->qty;
                $store_id = $product->store_id;
            }

            $order->sub_total = $total;

            $delivery_cost = 0 ;

            $delivery = new Delivery();

            $address = Address::where('id',$address_id)
                ->where('customer_id',$customer_id)->first();


            $delivery_cost = $delivery->get_delivery_costForUserCurrency($address, Store::find($store_id));

            $order->delivery_cost = $delivery_cost;
            $order->total = $total+$delivery_cost;
            $order->store_id = $store_id;
            $order->currency_id = optional(getCurrentUserCurrency())->id;
            $order->save();


            // send to store jan
            $receiver=Customer::find($customer_id);
            $order_item=OrderItem::where('order_id',$order->id)->get();


            //dd($new_products);
            OrderTransaction::save($order,"order");
            CartApi::where('customer_id',$customer_id)->delete();


            SendNotification::customerSendOrderToStore($order);

            CandidateTable::save($order,"order");

            \DB::commit();
            return $this->sendResponse(null,\Lang::get("lang.order_sent_successfully"));
        } catch (\Exception $e) {
            file_put_contents('cart_api_payment_error.log',print_r($e->getMessage(),true).'\n',FILE_APPEND);
            \DB::rollback();
            return $this->sendError($e->getMessage(),500);
        }

    }


}
